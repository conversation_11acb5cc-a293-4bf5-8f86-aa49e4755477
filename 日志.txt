[16:31:59] 步骤13.10: 执行智能OCR分析...
[16:31:59] ==================================================
[16:31:59] 🧠 智能OCR分析开始
[16:31:59] ==================================================
[16:31:59] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[16:31:59] ✅ OCR截图已保存: 商品图片\7.jpg
[16:32:05] 正在执行OCR识别...
[16:32:07] OCR原始结果类型: <class 'list'>
[16:32:07] OCR原始结果长度: 1
[16:32:07] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:07] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[16:32:07]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[16:32:07]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002D5077B1A60>
[16:32:07]   _rand_fn: <class 'NoneType'> - None
[16:32:07]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002D5056585C0>
[16:32:07] 方式2成功：识别到 18 个文本
[16:32:07] 文本 0: '确认款式', 置信度: 0.9998
[16:32:07] OCR识别: 确认款式 (置信度: 0.9998)
[16:32:07] 文本 1: '×', 置信度: 0.9941
[16:32:07] OCR识别: × (置信度: 0.9941)
[16:32:07] 文本 2: '￥35.96-69.23', 置信度: 0.9722
[16:32:07] OCR识别: ￥35.96-69.23 (置信度: 0.9722)
[16:32:07] 文本 3: '1', 置信度: 0.9995
[16:32:07] OCR识别: 1 (置信度: 0.9995)
[16:32:07] 文本 4: '+', 置信度: 0.9934
[16:32:07] OCR识别: + (置信度: 0.9934)
[16:32:07] 文本 5: '2件9.9折', 置信度: 0.9995
[16:32:07] OCR识别: 2件9.9折 (置信度: 0.9995)
[16:32:07] 文本 6: '请选择：颜色分类参考分类', 置信度: 0.9856
[16:32:07] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9856)
[16:32:07] 文本 7: '颜色分类', 置信度: 0.9998
[16:32:07] OCR识别: 颜色分类 (置信度: 0.9998)
[16:32:07] 文本 8: '藏青色三件套', 置信度: 0.9978
[16:32:07] OCR识别: 藏青色三件套 (置信度: 0.9978)
[16:32:07] 文本 9: '焦糖色三件套', 置信度: 0.9994
[16:32:07] OCR识别: 焦糖色三件套 (置信度: 0.9994)
[16:32:07] 文本 10: '单件短裙黑色', 置信度: 0.9991
[16:32:07] OCR识别: 单件短裙黑色 (置信度: 0.9991)
[16:32:07] 文本 11: '参考分类', 置信度: 0.9999
[16:32:07] OCR识别: 参考分类 (置信度: 0.9999)
[16:32:07] 文本 12: '90建议身高80-90', 置信度: 0.9991
[16:32:07] OCR识别: 90建议身高80-90 (置信度: 0.9991)
[16:32:07] 文本 13: '100建议身高90-100', 置信度: 0.9970
[16:32:07] OCR识别: 100建议身高90-100 (置信度: 0.9970)
[16:32:07] 文本 14: '110建议身高100-110', 置信度: 0.9812
[16:32:07] OCR识别: 110建议身高100-110 (置信度: 0.9812)
[16:32:07] 文本 15: '120建议身高110-120', 置信度: 0.9983
[16:32:07] OCR识别: 120建议身高110-120 (置信度: 0.9983)
[16:32:07] 文本 16: '130建议身高120-130', 置信度: 0.9964
[16:32:07] OCR识别: 130建议身高120-130 (置信度: 0.9964)
[16:32:07] 文本 17: '140建议身高130-140', 置信度: 0.9930
[16:32:07] OCR识别: 140建议身高130-140 (置信度: 0.9930)
[16:32:07] ✅ OCR识别完成，共识别到 18 个文本
[16:32:07] 💾 保存OCR结果到文件...
[16:32:07] ✅ OCR结果已保存: OCR\商品7_OCR结果.txt
[16:32:07] 🧠 开始智能分析商品信息...
[16:32:07] 🔧 开始智能拼接被分割的文本...
[16:32:07] 🎯 找到上方紧贴文本:
[16:32:07]    上方文本: '￥35.96-69.23' Y范围: 117.0-151.0, X范围: 0.0-176.0
[16:32:07]    当前文本: '2件9.9折' Y范围: 152.0-174.0, X起始: 5.0
[16:32:07]    垂直间距: 1.0px, X重叠: True
[16:32:07] ✅ 向上拼接: '￥35.96-69.23' + '2件9.9折' = '￥35.96-69.232件9.9折'
[16:32:07] 🔗 完成向上拼接:
[16:32:07]    结果: '￥35.96-69.232件9.9折'
[16:32:07] 🎯 找到上方紧贴文本:
[16:32:07]    上方文本: '请选择：颜色分类参考分类' Y范围: 175.0-197.0, X范围: 4.0-179.0
[16:32:07]    当前文本: '颜色分类' Y范围: 211.0-237.0, X起始: 2.0
[16:32:07]    垂直间距: 14.0px, X重叠: True
[16:32:07] ✅ 向上拼接: '请选择：颜色分类参考分类' + '颜色分类' = '请选择：颜色分类参考分类颜色分类'
[16:32:07] 🔗 完成向上拼接:
[16:32:07]    结果: '请选择：颜色分类参考分类颜色分类'
[16:32:07] 🎯 找到上方紧贴文本:
[16:32:07]    上方文本: '参考分类' Y范围: 407.0-436.0, X范围: 1.0-74.0
[16:32:07]    当前文本: '90建议身高80-90' Y范围: 446.0-467.0, X起始: 16.0
[16:32:07]    垂直间距: 10.0px, X重叠: True
[16:32:07] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[16:32:07] 🔗 完成向上拼接:
[16:32:07]    结果: '参考分类90建议身高80-90'
[16:32:07] 📊 拼接结果: 原始18个文本 → 拼接后15个文本
[16:32:07] 📝 拼接后的文本列表:
[16:32:07]    1. '确认款式'
[16:32:07]    2. '×'
[16:32:07]    3. '1'
[16:32:07]    4. '+'
[16:32:07]    5. '￥35.96-69.232件9.9折'
[16:32:07]    6. '请选择：颜色分类参考分类颜色分类'
[16:32:07]    7. '藏青色三件套'
[16:32:07]    8. '焦糖色三件套'
[16:32:07]    9. '单件短裙黑色'
[16:32:07]    10. '参考分类90建议身高80-90'
[16:32:07]    11. '100建议身高90-100'
[16:32:07]    12. '110建议身高100-110'
[16:32:07]    13. '120建议身高110-120'
[16:32:07]    14. '130建议身高120-130'
[16:32:07]    15. '140建议身高130-140'
[16:32:07] 🎯 找到尺码区域开始位置: 第6行 '请选择：颜色分类参考分类颜色分类'
[16:32:07] 📝 尺码区域文本无数字: '藏青色三件套'
[16:32:07] 📝 尺码区域文本无数字: '焦糖色三件套'
[16:32:07] 📝 尺码区域文本无数字: '单件短裙黑色'
[16:32:07] ✅ 提取尺码: 90 (来源: '参考分类90建议身高80-90')
[16:32:07] 🔍 处理bbox: [1, 407, 74, 436] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (37, 421)
[16:32:07] 📍 记录尺码坐标: 90 -> (37, 421)
[16:32:07] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[16:32:07] 🔍 处理bbox: [184, 446, 339, 467] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (261, 456)
[16:32:07] 📍 记录尺码坐标: 100 -> (261, 456)
[16:32:07] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[16:32:07] 🔍 处理bbox: [16, 488, 176, 509] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (96, 498)
[16:32:07] 📍 记录尺码坐标: 110 -> (96, 498)
[16:32:07] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[16:32:07] 🔍 处理bbox: [205, 488, 366, 509] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (285, 498)
[16:32:07] 📍 记录尺码坐标: 120 -> (285, 498)
[16:32:07] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[16:32:07] 🔍 处理bbox: [16, 530, 178, 551] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (97, 540)
[16:32:07] 📍 记录尺码坐标: 130 -> (97, 540)
[16:32:07] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[16:32:07] 🔍 处理bbox: [208, 531, 368, 549] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (288, 540)
[16:32:07] 📍 记录尺码坐标: 140 -> (288, 540)
[16:32:07] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[16:32:07] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[16:32:07] 📍 已记录尺码坐标: {90: (37, 421), 100: (261, 456), 110: (96, 498), 120: (285, 498), 130: (97, 540), 140: (288, 540)}
[16:32:07] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类颜色分类' → 提取: '参考分类颜色分类'
[16:32:07] 🎯 找到颜色分类开始位置: 第5行 '请选择：颜色分类参考分类颜色分类'
[16:32:07] 🎯 找到颜色分类结束位置: 第9行 '参考分类90建议身高80-90'
[16:32:07] 🔍 开始提取颜色分类: 从第6行到第8行
[16:32:07] ✅ 保留有效文本: '藏青色三件套'
[16:32:07] ✅ 保留有效文本: '焦糖色三件套'
[16:32:07] ✅ 保留有效文本: '单件短裙黑色'
[16:32:07] 🔍 检查颜色文本: '藏青色三件套' (长度: 6)
[16:32:07] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 31 ... 385]
[16:32:07] 🔍 价格检测结果: False
[16:32:07] 🔍 处理bbox: [31, 365, 123, 385] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (77, 375)
[16:32:07] 📍 记录颜色坐标: 藏青色三件套 -> (77, 375)
[16:32:07] ✅ 提取到无价格颜色: 藏青色三件套
[16:32:07] 🔍 检查颜色文本: '焦糖色三件套' (长度: 6)
[16:32:07] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [165 ... 385]
[16:32:07] 🔍 价格检测结果: False
[16:32:07] 🔍 处理bbox: [165, 365, 255, 385] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (210, 375)
[16:32:07] 📍 记录颜色坐标: 焦糖色三件套 -> (210, 375)
[16:32:07] ✅ 提取到无价格颜色: 焦糖色三件套
[16:32:07] 🔍 检查颜色文本: '单件短裙黑色' (长度: 6)
[16:32:07] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [305 ... 385]
[16:32:07] 🔍 价格检测结果: False
[16:32:07] 🔍 处理bbox: [305, 365, 394, 385] (长度: 4)
[16:32:07] ✅ 计算坐标成功: (349, 375)
[16:32:07] 📍 记录颜色坐标: 单件短裙黑色 -> (349, 375)
[16:32:07] ✅ 提取到无价格颜色: 单件短裙黑色
[16:32:07] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[16:32:07] 📍 坐标记录完成: 共记录 3 个坐标
[16:32:07] 提取到颜色分类: [{'pure_name': '藏青色三件套', 'original_text': '藏青色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 31, ..., 385], dtype=int16)}, {'pure_name': '焦糖色三件套', 'original_text': '焦糖色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([165, ..., 385], dtype=int16)}, {'pure_name': '单件短裙黑色', 'original_text': '单件短裙黑色', 'has_direct_price': False, 'direct_price': None, 'bbox': array([305, ..., 385], dtype=int16)}]
[16:32:07] 颜色分类数量: 3
[16:32:07] 📍 已记录颜色坐标: {'藏青色三件套': (77, 375), '焦糖色三件套': (210, 375), '单件短裙黑色': (349, 375)}
[16:32:07] 🎯 从页面提取价格信息
[16:32:07] ✅ 页面通用价格: 35.96 (来源: ￥35.96-69.232件9.9折)
[16:32:07] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[16:32:07] 🔍 商品类型分析:
[16:32:07]    颜色数量: 3
[16:32:07]    颜色直接带价格: False
[16:32:07]    页面有券前/券后价格: False
[16:32:07]    尺码带价格: False
[16:32:07] 📊 分析类型: type3_multiple_colors_no_prices
[16:32:07] 🔧 处理方案: interactive
[16:32:07] 📊 智能分析结果:
[16:32:07]   优化尺码范围: 90-140
[16:32:07]   原始尺码文本: ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140']
[16:32:07]   颜色分类: ['藏青色三件套', '焦糖色三件套', '单件短裙黑色']
[16:32:07]   颜色价格: {}
[16:32:07]   分析类型: type3_multiple_colors_no_prices
[16:32:07]   处理方案: interactive
[16:32:07] 🔄 切换到交互式价格获取方案
[16:32:07] 🚀 开始交互式价格获取...
[16:32:07] 需要交互获取价格的颜色: ['藏青色三件套', '焦糖色三件套', '单件短裙黑色']
[16:32:07] 📏 尺码选择策略: 从6个尺码中选择中间值 120
[16:32:07]    完整尺码列表: [90, 100, 110, 120, 130, 140]
[16:32:07]    避免最小码: 90，避免最大码: 140
[16:32:07] 选择中间尺码: 120
[16:32:07] 🔍 检查尺码坐标记录: {90: (37, 421), 100: (261, 456), 110: (96, 498), 120: (285, 498), 130: (97, 540), 140: (288, 540)}
[16:32:07] 🔍 查找尺码: 120
[16:32:07] 📍 使用记录的尺码坐标: 120
[16:32:07]    相对坐标: (285, 498)
[16:32:07]    绝对坐标: (555, 728)
[16:32:07] 🎯 移动到尺码 120 坐标: (555, 728)
[16:32:08] 🎯 点击尺码 120
[16:32:10] 🎯 检测到多颜色商品，需要依次点击 3 个颜色
[16:32:10] 🎨 处理颜色 1/3: 藏青色三件套
[16:32:10] 📍 使用记录的坐标: 藏青色三件套
[16:32:10]    相对坐标: (77, 375)
[16:32:10]    绝对坐标: (347, 605)
[16:32:10] 🎯 移动到颜色 藏青色三件套 坐标: (347, 605)
[16:32:11] 🎯 点击颜色 藏青色三件套
[16:32:14] 📸 截取颜色 藏青色三件套 更新后的页面...
[16:32:14] ✅ 价格OCR截图已保存: 商品图片\7-1.jpg
[16:32:15] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:15] 字典格式价格OCR识别到 19 个文本
[16:32:15] 价格OCR识别: 确认款式 (置信度: 1.000)
[16:32:15] 价格OCR识别: × (置信度: 0.994)
[16:32:15] 价格OCR识别: ￥69.23 (置信度: 0.929)
[16:32:15] 价格OCR识别: 1 (置信度: 0.999)
[16:32:15] 价格OCR识别: + (置信度: 0.993)
[16:32:15] 价格OCR识别: 2件9.9折 (置信度: 0.999)
[16:32:15] 价格OCR识别: 已选择：藏青色三件套120建议身高110-120 (置信度: 0.997)
[16:32:15] 价格OCR识别: 已拼4.8万+件 (置信度: 0.995)
[16:32:15] 价格OCR识别: 颜色分类 (置信度: 1.000)
[16:32:15] 价格OCR识别: 藏青色三件套 (置信度: 0.999)
[16:32:15] 价格OCR识别: 焦糖色三件套 (置信度: 0.999)
[16:32:15] 价格OCR识别: 单件短裙黑色 (置信度: 0.999)
[16:32:15] 价格OCR识别: 参考分类 (置信度: 1.000)
[16:32:15] 价格OCR识别: 90建议身高80-90 (置信度: 0.998)
[16:32:15] 价格OCR识别: 100建议身高90-100 (置信度: 0.997)
[16:32:15] 价格OCR识别: 110建议身高100-110 (置信度: 0.981)
[16:32:15] 价格OCR识别: 120建议身高110-120 (置信度: 0.999)
[16:32:15] 价格OCR识别: 130建议身高120-130 (置信度: 0.996)
[16:32:15] 价格OCR识别: 140建议身高130-140 (置信度: 0.993)
[16:32:15] ✅ 价格OCR识别完成，共识别到 19 个文本
[16:32:15] ✅ 提取到通用价格: 69.23 (来源: ￥69.23)
[16:32:15] ✅ 获取到颜色 藏青色三件套 的价格: 69.23
[16:32:15] 🔍 开始第2次坐标校验OCR识别...
[16:32:15] 🔍 执行坐标校验OCR识别...
[16:32:15] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[16:32:15] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[16:32:15] 正在执行OCR识别...
[16:32:17] OCR原始结果类型: <class 'list'>
[16:32:17] OCR原始结果长度: 1
[16:32:17] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:17] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[16:32:17]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[16:32:17]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002D502918740>
[16:32:17]   _rand_fn: <class 'NoneType'> - None
[16:32:17]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002D507955040>
[16:32:17] 方式2成功：识别到 19 个文本
[16:32:17] 文本 0: '确认款式', 置信度: 0.9997
[16:32:17] OCR识别: 确认款式 (置信度: 0.9997)
[16:32:17] 文本 1: '×', 置信度: 0.9943
[16:32:17] OCR识别: × (置信度: 0.9943)
[16:32:17] 文本 2: '￥69.23', 置信度: 0.9291
[16:32:17] OCR识别: ￥69.23 (置信度: 0.9291)
[16:32:17] 文本 3: '1', 置信度: 0.9995
[16:32:17] OCR识别: 1 (置信度: 0.9995)
[16:32:17] 文本 4: '+', 置信度: 0.9938
[16:32:17] OCR识别: + (置信度: 0.9938)
[16:32:17] 文本 5: '2件9.9折', 置信度: 0.9993
[16:32:17] OCR识别: 2件9.9折 (置信度: 0.9993)
[16:32:17] 文本 6: '已选择：藏青色三件套120建议身高110-120', 置信度: 0.9967
[16:32:17] OCR识别: 已选择：藏青色三件套120建议身高110-120 (置信度: 0.9967)
[16:32:17] 文本 7: '已拼4.8万+件', 置信度: 0.9945
[16:32:17] OCR识别: 已拼4.8万+件 (置信度: 0.9945)
[16:32:17] 文本 8: '颜色分类', 置信度: 0.9998
[16:32:17] OCR识别: 颜色分类 (置信度: 0.9998)
[16:32:17] 文本 9: '藏青色三件套', 置信度: 0.9990
[16:32:17] OCR识别: 藏青色三件套 (置信度: 0.9990)
[16:32:17] 文本 10: '焦糖色三件套', 置信度: 0.9995
[16:32:17] OCR识别: 焦糖色三件套 (置信度: 0.9995)
[16:32:17] 文本 11: '单件短裙黑色', 置信度: 0.9990
[16:32:17] OCR识别: 单件短裙黑色 (置信度: 0.9990)
[16:32:17] 文本 12: '参考分类', 置信度: 1.0000
[16:32:17] OCR识别: 参考分类 (置信度: 1.0000)
[16:32:17] 文本 13: '90建议身高80-90', 置信度: 0.9990
[16:32:17] OCR识别: 90建议身高80-90 (置信度: 0.9990)
[16:32:17] 文本 14: '100建议身高90-100', 置信度: 0.9972
[16:32:17] OCR识别: 100建议身高90-100 (置信度: 0.9972)
[16:32:17] 文本 15: '110建议身高100-110', 置信度: 0.9784
[16:32:17] OCR识别: 110建议身高100-110 (置信度: 0.9784)
[16:32:17] 文本 16: '120建议身高110-120', 置信度: 0.9988
[16:32:17] OCR识别: 120建议身高110-120 (置信度: 0.9988)
[16:32:17] 文本 17: '130建议身高120-130', 置信度: 0.9964
[16:32:17] OCR识别: 130建议身高120-130 (置信度: 0.9964)
[16:32:17] 文本 18: '140建议身高130-140', 置信度: 0.9960
[16:32:17] OCR识别: 140建议身高130-140 (置信度: 0.9960)
[16:32:17] ✅ OCR识别完成，共识别到 19 个文本
[16:32:17] 🧠 开始智能分析商品信息...
[16:32:17] 🔧 开始智能拼接被分割的文本...
[16:32:17] 🎯 找到上方紧贴文本:
[16:32:17]    上方文本: '￥69.23' Y范围: 117.0-150.0, X范围: 1.0-94.0
[16:32:17]    当前文本: '2件9.9折' Y范围: 152.0-174.0, X起始: 6.0
[16:32:17]    垂直间距: 2.0px, X重叠: True
[16:32:17] ✅ 向上拼接: '￥69.23' + '2件9.9折' = '￥69.232件9.9折'
[16:32:17] 🔗 完成向上拼接:
[16:32:17]    结果: '￥69.232件9.9折'
[16:32:17] 🎯 找到上方紧贴文本:
[16:32:17]    上方文本: '参考分类' Y范围: 410.0-436.0, X范围: 2.0-74.0
[16:32:17]    当前文本: '90建议身高80-90' Y范围: 446.0-467.0, X起始: 16.0
[16:32:17]    垂直间距: 10.0px, X重叠: True
[16:32:17] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[16:32:17] 🔗 完成向上拼接:
[16:32:17]    结果: '参考分类90建议身高80-90'
[16:32:17] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[16:32:17] 📝 拼接后的文本列表:
[16:32:17]    1. '确认款式'
[16:32:17]    2. '×'
[16:32:17]    3. '1'
[16:32:17]    4. '+'
[16:32:17]    5. '￥69.232件9.9折'
[16:32:17]    6. '已选择：藏青色三件套120建议身高110-120'
[16:32:17]    7. '已拼4.8万+件'
[16:32:17]    8. '颜色分类'
[16:32:17]    9. '藏青色三件套'
[16:32:17]    10. '焦糖色三件套'
[16:32:17]    11. '单件短裙黑色'
[16:32:17]    12. '参考分类90建议身高80-90'
[16:32:17]    13. '100建议身高90-100'
[16:32:17]    14. '110建议身高100-110'
[16:32:17]    15. '120建议身高110-120'
[16:32:17]    16. '130建议身高120-130'
[16:32:17]    17. '140建议身高130-140'
[16:32:17] 🎯 找到尺码区域开始位置: 第12行 '参考分类90建议身高80-90'
[16:32:17] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[16:32:17] 🔍 处理bbox: [184, 446, 339, 467] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (261, 456)
[16:32:17] 📍 记录尺码坐标: 100 -> (261, 456)
[16:32:17] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[16:32:17] 🔍 处理bbox: [16, 488, 176, 509] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (96, 498)
[16:32:17] 📍 记录尺码坐标: 110 -> (96, 498)
[16:32:17] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[16:32:17] 🔍 处理bbox: [205, 488, 366, 509] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (285, 498)
[16:32:17] 📍 记录尺码坐标: 120 -> (285, 498)
[16:32:17] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[16:32:17] 🔍 处理bbox: [16, 530, 178, 551] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (97, 540)
[16:32:17] 📍 记录尺码坐标: 130 -> (97, 540)
[16:32:17] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[16:32:17] 🔍 处理bbox: [208, 531, 368, 549] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (288, 540)
[16:32:17] 📍 记录尺码坐标: 140 -> (288, 540)
[16:32:17] 📊 尺码提取结果: 数字=[100, 110, 120, 130, 140], 范围=100-140, 原始文本数量=5
[16:32:17] 尺码信息: {'optimized_range': '100-140', 'original_texts': ['100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [100, 110, 120, 130, 140]}
[16:32:17] 📍 已记录尺码坐标: {90: (37, 421), 100: (261, 456), 110: (96, 498), 120: (285, 498), 130: (97, 540), 140: (288, 540)}
[16:32:17] 🎯 找到颜色分类开始位置: 第7行 '颜色分类'
[16:32:17] 🎯 找到颜色分类结束位置: 第11行 '参考分类90建议身高80-90'
[16:32:17] 🔍 开始提取颜色分类: 从第8行到第10行
[16:32:17] ✅ 保留有效文本: '藏青色三件套'
[16:32:17] ✅ 保留有效文本: '焦糖色三件套'
[16:32:17] ✅ 保留有效文本: '单件短裙黑色'
[16:32:17] 🔍 检查颜色文本: '藏青色三件套' (长度: 6)
[16:32:17] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 31 ... 385]
[16:32:17] 🔍 价格检测结果: False
[16:32:17] 🔍 处理bbox: [31, 365, 124, 385] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (77, 375)
[16:32:17] 📍 记录颜色坐标: 藏青色三件套 -> (77, 375)
[16:32:17] ✅ 提取到无价格颜色: 藏青色三件套
[16:32:17] 🔍 检查颜色文本: '焦糖色三件套' (长度: 6)
[16:32:17] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [165 ... 385]
[16:32:17] 🔍 价格检测结果: False
[16:32:17] 🔍 处理bbox: [165, 365, 255, 385] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (210, 375)
[16:32:17] 📍 记录颜色坐标: 焦糖色三件套 -> (210, 375)
[16:32:17] ✅ 提取到无价格颜色: 焦糖色三件套
[16:32:17] 🔍 检查颜色文本: '单件短裙黑色' (长度: 6)
[16:32:17] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [305 ... 385]
[16:32:17] 🔍 价格检测结果: False
[16:32:17] 🔍 处理bbox: [305, 365, 394, 385] (长度: 4)
[16:32:17] ✅ 计算坐标成功: (349, 375)
[16:32:17] 📍 记录颜色坐标: 单件短裙黑色 -> (349, 375)
[16:32:17] ✅ 提取到无价格颜色: 单件短裙黑色
[16:32:17] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[16:32:17] 📍 坐标记录完成: 共记录 3 个坐标
[16:32:17] 提取到颜色分类: [{'pure_name': '藏青色三件套', 'original_text': '藏青色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 31, ..., 385], dtype=int16)}, {'pure_name': '焦糖色三件套', 'original_text': '焦糖色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([165, ..., 385], dtype=int16)}, {'pure_name': '单件短裙黑色', 'original_text': '单件短裙黑色', 'has_direct_price': False, 'direct_price': None, 'bbox': array([305, ..., 385], dtype=int16)}]
[16:32:17] 颜色分类数量: 3
[16:32:17] 📍 已记录颜色坐标: {'藏青色三件套': (77, 375), '焦糖色三件套': (210, 375), '单件短裙黑色': (349, 375)}
[16:32:17] 🎯 从页面提取价格信息
[16:32:17] ✅ 页面通用价格: 69.232 (来源: ￥69.232件9.9折)
[16:32:17] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[16:32:17] 🔍 商品类型分析:
[16:32:17]    颜色数量: 3
[16:32:17]    颜色直接带价格: False
[16:32:17]    页面有券前/券后价格: False
[16:32:17]    尺码带价格: False
[16:32:17] 📊 分析类型: type3_multiple_colors_no_prices
[16:32:17] 🔧 处理方案: interactive
[16:32:17] 📊 坐标对比结果（仅对比原有颜色）:
[16:32:17]    藏青色三件套: (77, 375) → (77, 375) (差异: X±0, Y±0)
[16:32:17]    ✅ 藏青色三件套 坐标无显著变化
[16:32:17]    焦糖色三件套: (210, 375) → (210, 375) (差异: X±0, Y±0)
[16:32:17]    ✅ 焦糖色三件套 坐标无显著变化
[16:32:17]    单件短裙黑色: (349, 375) → (349, 375) (差异: X±0, Y±0)
[16:32:17]    ✅ 单件短裙黑色 坐标无显著变化
[16:32:17] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[16:32:17] ✅ 坐标无变化，继续使用原始坐标
[16:32:17] 🎨 处理颜色 2/3: 焦糖色三件套
[16:32:17] 📍 使用记录的坐标: 焦糖色三件套
[16:32:17]    相对坐标: (210, 375)
[16:32:17]    绝对坐标: (480, 605)
[16:32:17] 🎯 移动到颜色 焦糖色三件套 坐标: (480, 605)
[16:32:18] 🎯 点击颜色 焦糖色三件套
[16:32:21] 📸 截取颜色 焦糖色三件套 更新后的页面...
[16:32:21] ✅ 价格OCR截图已保存: 商品图片\7-2.jpg
[16:32:22] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:22] 字典格式价格OCR识别到 19 个文本
[16:32:22] 价格OCR识别: 确认款式 (置信度: 1.000)
[16:32:22] 价格OCR识别: × (置信度: 0.994)
[16:32:22] 价格OCR识别: ￥69.23 (置信度: 0.929)
[16:32:22] 价格OCR识别: 1 (置信度: 0.999)
[16:32:22] 价格OCR识别: + (置信度: 0.994)
[16:32:22] 价格OCR识别: 2件9.9折 (置信度: 0.999)
[16:32:22] 价格OCR识别: 已选择：焦糖色三件套120建议身高110-120 (置信度: 0.997)
[16:32:22] 价格OCR识别: 已拼4.8万+件 (置信度: 0.994)
[16:32:22] 价格OCR识别: 颜色分类 (置信度: 1.000)
[16:32:22] 价格OCR识别: 藏青色三件套 (置信度: 0.998)
[16:32:22] 价格OCR识别: 焦糖色三件套 (置信度: 1.000)
[16:32:22] 价格OCR识别: 单件短裙黑色 (置信度: 0.999)
[16:32:22] 价格OCR识别: 参考分类 (置信度: 1.000)
[16:32:22] 价格OCR识别: 90建议身高80-90 (置信度: 0.999)
[16:32:22] 价格OCR识别: 100建议身高90-100 (置信度: 0.997)
[16:32:22] 价格OCR识别: 110建议身高100-110 (置信度: 0.978)
[16:32:22] 价格OCR识别: 120建议身高110-120 (置信度: 0.999)
[16:32:22] 价格OCR识别: 130建议身高120-130 (置信度: 0.996)
[16:32:22] 价格OCR识别: 140建议身高130-140 (置信度: 0.996)
[16:32:22] ✅ 价格OCR识别完成，共识别到 19 个文本
[16:32:22] ✅ 提取到通用价格: 69.23 (来源: ￥69.23)
[16:32:22] ✅ 获取到颜色 焦糖色三件套 的价格: 69.23
[16:32:22] 🔍 开始第3次坐标校验OCR识别...
[16:32:22] 🔍 执行坐标校验OCR识别...
[16:32:22] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[16:32:22] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[16:32:22] 正在执行OCR识别...
[16:32:24] OCR原始结果类型: <class 'list'>
[16:32:24] OCR原始结果长度: 1
[16:32:24] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:24] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[16:32:24]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[16:32:24]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002D507844500>
[16:32:24]   _rand_fn: <class 'NoneType'> - None
[16:32:24]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002D507957110>
[16:32:24] 方式2成功：识别到 19 个文本
[16:32:24] 文本 0: '确认款式', 置信度: 0.9997
[16:32:24] OCR识别: 确认款式 (置信度: 0.9997)
[16:32:24] 文本 1: '×', 置信度: 0.9943
[16:32:24] OCR识别: × (置信度: 0.9943)
[16:32:24] 文本 2: '￥69.23', 置信度: 0.9291
[16:32:24] OCR识别: ￥69.23 (置信度: 0.9291)
[16:32:24] 文本 3: '1', 置信度: 0.9995
[16:32:24] OCR识别: 1 (置信度: 0.9995)
[16:32:24] 文本 4: '+', 置信度: 0.9938
[16:32:24] OCR识别: + (置信度: 0.9938)
[16:32:24] 文本 5: '2件9.9折', 置信度: 0.9993
[16:32:24] OCR识别: 2件9.9折 (置信度: 0.9993)
[16:32:24] 文本 6: '已选择：焦糖色三件套120建议身高110-120', 置信度: 0.9970
[16:32:24] OCR识别: 已选择：焦糖色三件套120建议身高110-120 (置信度: 0.9970)
[16:32:24] 文本 7: '已拼4.8万+件', 置信度: 0.9945
[16:32:24] OCR识别: 已拼4.8万+件 (置信度: 0.9945)
[16:32:24] 文本 8: '颜色分类', 置信度: 0.9998
[16:32:24] OCR识别: 颜色分类 (置信度: 0.9998)
[16:32:24] 文本 9: '藏青色三件套', 置信度: 0.9985
[16:32:24] OCR识别: 藏青色三件套 (置信度: 0.9985)
[16:32:24] 文本 10: '焦糖色三件套', 置信度: 0.9997
[16:32:24] OCR识别: 焦糖色三件套 (置信度: 0.9997)
[16:32:24] 文本 11: '单件短裙黑色', 置信度: 0.9990
[16:32:24] OCR识别: 单件短裙黑色 (置信度: 0.9990)
[16:32:24] 文本 12: '参考分类', 置信度: 0.9999
[16:32:24] OCR识别: 参考分类 (置信度: 0.9999)
[16:32:24] 文本 13: '90建议身高80-90', 置信度: 0.9987
[16:32:24] OCR识别: 90建议身高80-90 (置信度: 0.9987)
[16:32:24] 文本 14: '100建议身高90-100', 置信度: 0.9972
[16:32:24] OCR识别: 100建议身高90-100 (置信度: 0.9972)
[16:32:24] 文本 15: '110建议身高100-110', 置信度: 0.9784
[16:32:24] OCR识别: 110建议身高100-110 (置信度: 0.9784)
[16:32:24] 文本 16: '120建议身高110-120', 置信度: 0.9988
[16:32:24] OCR识别: 120建议身高110-120 (置信度: 0.9988)
[16:32:24] 文本 17: '130建议身高120-130', 置信度: 0.9964
[16:32:24] OCR识别: 130建议身高120-130 (置信度: 0.9964)
[16:32:24] 文本 18: '140建议身高130-140', 置信度: 0.9960
[16:32:24] OCR识别: 140建议身高130-140 (置信度: 0.9960)
[16:32:24] ✅ OCR识别完成，共识别到 19 个文本
[16:32:24] 🧠 开始智能分析商品信息...
[16:32:24] 🔧 开始智能拼接被分割的文本...
[16:32:24] 🎯 找到上方紧贴文本:
[16:32:24]    上方文本: '￥69.23' Y范围: 117.0-150.0, X范围: 1.0-94.0
[16:32:24]    当前文本: '2件9.9折' Y范围: 152.0-174.0, X起始: 6.0
[16:32:24]    垂直间距: 2.0px, X重叠: True
[16:32:24] ✅ 向上拼接: '￥69.23' + '2件9.9折' = '￥69.232件9.9折'
[16:32:24] 🔗 完成向上拼接:
[16:32:24]    结果: '￥69.232件9.9折'
[16:32:24] 🎯 找到上方紧贴文本:
[16:32:24]    上方文本: '参考分类' Y范围: 407.0-436.0, X范围: 1.0-74.0
[16:32:24]    当前文本: '90建议身高80-90' Y范围: 446.0-467.0, X起始: 16.0
[16:32:24]    垂直间距: 10.0px, X重叠: True
[16:32:24] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[16:32:24] 🔗 完成向上拼接:
[16:32:24]    结果: '参考分类90建议身高80-90'
[16:32:24] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[16:32:24] 📝 拼接后的文本列表:
[16:32:24]    1. '确认款式'
[16:32:24]    2. '×'
[16:32:24]    3. '1'
[16:32:24]    4. '+'
[16:32:24]    5. '￥69.232件9.9折'
[16:32:24]    6. '已选择：焦糖色三件套120建议身高110-120'
[16:32:24]    7. '已拼4.8万+件'
[16:32:24]    8. '颜色分类'
[16:32:24]    9. '藏青色三件套'
[16:32:24]    10. '焦糖色三件套'
[16:32:24]    11. '单件短裙黑色'
[16:32:24]    12. '参考分类90建议身高80-90'
[16:32:24]    13. '100建议身高90-100'
[16:32:24]    14. '110建议身高100-110'
[16:32:24]    15. '120建议身高110-120'
[16:32:24]    16. '130建议身高120-130'
[16:32:24]    17. '140建议身高130-140'
[16:32:24] 🎯 找到尺码区域开始位置: 第12行 '参考分类90建议身高80-90'
[16:32:24] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[16:32:24] 🔍 处理bbox: [184, 446, 339, 467] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (261, 456)
[16:32:24] 📍 记录尺码坐标: 100 -> (261, 456)
[16:32:24] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[16:32:24] 🔍 处理bbox: [16, 488, 176, 509] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (96, 498)
[16:32:24] 📍 记录尺码坐标: 110 -> (96, 498)
[16:32:24] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[16:32:24] 🔍 处理bbox: [205, 488, 366, 509] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (285, 498)
[16:32:24] 📍 记录尺码坐标: 120 -> (285, 498)
[16:32:24] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[16:32:24] 🔍 处理bbox: [16, 530, 178, 551] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (97, 540)
[16:32:24] 📍 记录尺码坐标: 130 -> (97, 540)
[16:32:24] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[16:32:24] 🔍 处理bbox: [208, 531, 368, 549] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (288, 540)
[16:32:24] 📍 记录尺码坐标: 140 -> (288, 540)
[16:32:24] 📊 尺码提取结果: 数字=[100, 110, 120, 130, 140], 范围=100-140, 原始文本数量=5
[16:32:24] 尺码信息: {'optimized_range': '100-140', 'original_texts': ['100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [100, 110, 120, 130, 140]}
[16:32:24] 📍 已记录尺码坐标: {90: (37, 421), 100: (261, 456), 110: (96, 498), 120: (285, 498), 130: (97, 540), 140: (288, 540)}
[16:32:24] 🎯 找到颜色分类开始位置: 第7行 '颜色分类'
[16:32:24] 🎯 找到颜色分类结束位置: 第11行 '参考分类90建议身高80-90'
[16:32:24] 🔍 开始提取颜色分类: 从第8行到第10行
[16:32:24] ✅ 保留有效文本: '藏青色三件套'
[16:32:24] ✅ 保留有效文本: '焦糖色三件套'
[16:32:24] ✅ 保留有效文本: '单件短裙黑色'
[16:32:24] 🔍 检查颜色文本: '藏青色三件套' (长度: 6)
[16:32:24] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 32 ... 385]
[16:32:24] 🔍 价格检测结果: False
[16:32:24] 🔍 处理bbox: [32, 365, 123, 385] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (77, 375)
[16:32:24] 📍 记录颜色坐标: 藏青色三件套 -> (77, 375)
[16:32:24] ✅ 提取到无价格颜色: 藏青色三件套
[16:32:24] 🔍 检查颜色文本: '焦糖色三件套' (长度: 6)
[16:32:24] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [165 ... 385]
[16:32:24] 🔍 价格检测结果: False
[16:32:24] 🔍 处理bbox: [165, 365, 256, 385] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (210, 375)
[16:32:24] 📍 记录颜色坐标: 焦糖色三件套 -> (210, 375)
[16:32:24] ✅ 提取到无价格颜色: 焦糖色三件套
[16:32:24] 🔍 检查颜色文本: '单件短裙黑色' (长度: 6)
[16:32:24] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [305 ... 385]
[16:32:24] 🔍 价格检测结果: False
[16:32:24] 🔍 处理bbox: [305, 365, 394, 385] (长度: 4)
[16:32:24] ✅ 计算坐标成功: (349, 375)
[16:32:24] 📍 记录颜色坐标: 单件短裙黑色 -> (349, 375)
[16:32:24] ✅ 提取到无价格颜色: 单件短裙黑色
[16:32:24] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[16:32:24] 📍 坐标记录完成: 共记录 3 个坐标
[16:32:24] 提取到颜色分类: [{'pure_name': '藏青色三件套', 'original_text': '藏青色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 32, ..., 385], dtype=int16)}, {'pure_name': '焦糖色三件套', 'original_text': '焦糖色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([165, ..., 385], dtype=int16)}, {'pure_name': '单件短裙黑色', 'original_text': '单件短裙黑色', 'has_direct_price': False, 'direct_price': None, 'bbox': array([305, ..., 385], dtype=int16)}]
[16:32:24] 颜色分类数量: 3
[16:32:24] 📍 已记录颜色坐标: {'藏青色三件套': (77, 375), '焦糖色三件套': (210, 375), '单件短裙黑色': (349, 375)}
[16:32:24] 🎯 从页面提取价格信息
[16:32:24] ✅ 页面通用价格: 69.232 (来源: ￥69.232件9.9折)
[16:32:24] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[16:32:24] 🔍 商品类型分析:
[16:32:24]    颜色数量: 3
[16:32:24]    颜色直接带价格: False
[16:32:24]    页面有券前/券后价格: False
[16:32:24]    尺码带价格: False
[16:32:24] 📊 分析类型: type3_multiple_colors_no_prices
[16:32:24] 🔧 处理方案: interactive
[16:32:24] 📊 坐标对比结果（仅对比原有颜色）:
[16:32:24]    藏青色三件套: (77, 375) → (77, 375) (差异: X±0, Y±0)
[16:32:24]    ✅ 藏青色三件套 坐标无显著变化
[16:32:24]    焦糖色三件套: (210, 375) → (210, 375) (差异: X±0, Y±0)
[16:32:24]    ✅ 焦糖色三件套 坐标无显著变化
[16:32:24]    单件短裙黑色: (349, 375) → (349, 375) (差异: X±0, Y±0)
[16:32:24]    ✅ 单件短裙黑色 坐标无显著变化
[16:32:24] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[16:32:24] ✅ 坐标无变化，继续使用原始坐标
[16:32:24] 🎨 处理颜色 3/3: 单件短裙黑色
[16:32:24] 📍 使用记录的坐标: 单件短裙黑色
[16:32:24]    相对坐标: (349, 375)
[16:32:24]    绝对坐标: (619, 605)
[16:32:24] 🎯 移动到颜色 单件短裙黑色 坐标: (619, 605)
[16:32:25] 🎯 点击颜色 单件短裙黑色
[16:32:28] 📸 截取颜色 单件短裙黑色 更新后的页面...
[16:32:28] ✅ 价格OCR截图已保存: 商品图片\7-3.jpg
[16:32:29] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:29] 字典格式价格OCR识别到 19 个文本
[16:32:29] 价格OCR识别: 确认款式 (置信度: 1.000)
[16:32:29] 价格OCR识别: × (置信度: 0.994)
[16:32:29] 价格OCR识别: ￥35.96 (置信度: 0.940)
[16:32:29] 价格OCR识别: 1 (置信度: 0.999)
[16:32:29] 价格OCR识别: + (置信度: 0.994)
[16:32:29] 价格OCR识别: 2件9.9折 (置信度: 0.999)
[16:32:29] 价格OCR识别: 已拼4.8万+件 (置信度: 0.994)
[16:32:29] 价格OCR识别: 已选择：单件短裙黑色120建议身高110-120 (置信度: 0.998)
[16:32:29] 价格OCR识别: 颜色分类 (置信度: 1.000)
[16:32:29] 价格OCR识别: 藏青色三件套 (置信度: 0.998)
[16:32:29] 价格OCR识别: 焦糖色三件套 (置信度: 0.999)
[16:32:29] 价格OCR识别: 单件短裙黑色 (置信度: 0.999)
[16:32:29] 价格OCR识别: 参考分类 (置信度: 1.000)
[16:32:29] 价格OCR识别: 90建议身高80-90 (置信度: 0.999)
[16:32:29] 价格OCR识别: 100建议身高90-100 (置信度: 0.997)
[16:32:29] 价格OCR识别: 110建议身高100-110 (置信度: 0.978)
[16:32:29] 价格OCR识别: 120建议身高110-120 (置信度: 0.999)
[16:32:29] 价格OCR识别: 130建议身高120-130 (置信度: 0.996)
[16:32:29] 价格OCR识别: 140建议身高130-140 (置信度: 0.996)
[16:32:29] ✅ 价格OCR识别完成，共识别到 19 个文本
[16:32:29] ✅ 提取到通用价格: 35.96 (来源: ￥35.96)
[16:32:29] ✅ 获取到颜色 单件短裙黑色 的价格: 35.96
[16:32:29] 🔍 开始第4次坐标校验OCR识别...
[16:32:29] 🔍 执行坐标校验OCR识别...
[16:32:29] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[16:32:29] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[16:32:29] 正在执行OCR识别...
[16:32:31] OCR原始结果类型: <class 'list'>
[16:32:31] OCR原始结果长度: 1
[16:32:31] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:32:31] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[16:32:31]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[16:32:31]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002D507955C10>
[16:32:31]   _rand_fn: <class 'NoneType'> - None
[16:32:31]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002D5009CD6D0>
[16:32:31] 方式2成功：识别到 19 个文本
[16:32:31] 文本 0: '确认款式', 置信度: 0.9997
[16:32:31] OCR识别: 确认款式 (置信度: 0.9997)
[16:32:31] 文本 1: '×', 置信度: 0.9943
[16:32:31] OCR识别: × (置信度: 0.9943)
[16:32:31] 文本 2: '￥35.96', 置信度: 0.9405
[16:32:31] OCR识别: ￥35.96 (置信度: 0.9405)
[16:32:31] 文本 3: '1', 置信度: 0.9995
[16:32:31] OCR识别: 1 (置信度: 0.9995)
[16:32:31] 文本 4: '+', 置信度: 0.9938
[16:32:31] OCR识别: + (置信度: 0.9938)
[16:32:31] 文本 5: '2件9.9折', 置信度: 0.9991
[16:32:31] OCR识别: 2件9.9折 (置信度: 0.9991)
[16:32:31] 文本 6: '已拼4.8万+件', 置信度: 0.9945
[16:32:31] OCR识别: 已拼4.8万+件 (置信度: 0.9945)
[16:32:31] 文本 7: '已选择：单件短裙黑色120建议身高110-120', 置信度: 0.9980
[16:32:31] OCR识别: 已选择：单件短裙黑色120建议身高110-120 (置信度: 0.9980)
[16:32:31] 文本 8: '颜色分类', 置信度: 0.9998
[16:32:31] OCR识别: 颜色分类 (置信度: 0.9998)
[16:32:31] 文本 9: '藏青色三件套', 置信度: 0.9985
[16:32:31] OCR识别: 藏青色三件套 (置信度: 0.9985)
[16:32:31] 文本 10: '焦糖色三件套', 置信度: 0.9995
[16:32:31] OCR识别: 焦糖色三件套 (置信度: 0.9995)
[16:32:31] 文本 11: '单件短裙黑色', 置信度: 0.9995
[16:32:31] OCR识别: 单件短裙黑色 (置信度: 0.9995)
[16:32:31] 文本 12: '参考分类', 置信度: 1.0000
[16:32:31] OCR识别: 参考分类 (置信度: 1.0000)
[16:32:31] 文本 13: '90建议身高80-90', 置信度: 0.9987
[16:32:31] OCR识别: 90建议身高80-90 (置信度: 0.9987)
[16:32:31] 文本 14: '100建议身高90-100', 置信度: 0.9972
[16:32:31] OCR识别: 100建议身高90-100 (置信度: 0.9972)
[16:32:31] 文本 15: '110建议身高100-110', 置信度: 0.9784
[16:32:31] OCR识别: 110建议身高100-110 (置信度: 0.9784)
[16:32:31] 文本 16: '120建议身高110-120', 置信度: 0.9993
[16:32:31] OCR识别: 120建议身高110-120 (置信度: 0.9993)
[16:32:31] 文本 17: '130建议身高120-130', 置信度: 0.9964
[16:32:31] OCR识别: 130建议身高120-130 (置信度: 0.9964)
[16:32:31] 文本 18: '140建议身高130-140', 置信度: 0.9960
[16:32:31] OCR识别: 140建议身高130-140 (置信度: 0.9960)
[16:32:31] ✅ OCR识别完成，共识别到 19 个文本
[16:32:31] 🧠 开始智能分析商品信息...
[16:32:31] 🔧 开始智能拼接被分割的文本...
[16:32:31] 🎯 找到上方紧贴文本:
[16:32:31]    上方文本: '￥35.96' Y范围: 117.0-150.0, X范围: 1.0-94.0
[16:32:31]    当前文本: '2件9.9折' Y范围: 152.0-174.0, X起始: 6.0
[16:32:31]    垂直间距: 2.0px, X重叠: True
[16:32:31] ✅ 向上拼接: '￥35.96' + '2件9.9折' = '￥35.962件9.9折'
[16:32:31] 🔗 完成向上拼接:
[16:32:31]    结果: '￥35.962件9.9折'
[16:32:31] 🎯 找到上方紧贴文本:
[16:32:31]    上方文本: '参考分类' Y范围: 410.0-436.0, X范围: 2.0-74.0
[16:32:31]    当前文本: '90建议身高80-90' Y范围: 446.0-467.0, X起始: 16.0
[16:32:31]    垂直间距: 10.0px, X重叠: True
[16:32:31] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[16:32:31] 🔗 完成向上拼接:
[16:32:31]    结果: '参考分类90建议身高80-90'
[16:32:31] 📊 拼接结果: 原始19个文本 → 拼接后17个文本
[16:32:31] 📝 拼接后的文本列表:
[16:32:31]    1. '确认款式'
[16:32:31]    2. '×'
[16:32:31]    3. '1'
[16:32:31]    4. '+'
[16:32:31]    5. '￥35.962件9.9折'
[16:32:31]    6. '已拼4.8万+件'
[16:32:31]    7. '已选择：单件短裙黑色120建议身高110-120'
[16:32:31]    8. '颜色分类'
[16:32:31]    9. '藏青色三件套'
[16:32:31]    10. '焦糖色三件套'
[16:32:31]    11. '单件短裙黑色'
[16:32:31]    12. '参考分类90建议身高80-90'
[16:32:31]    13. '100建议身高90-100'
[16:32:31]    14. '110建议身高100-110'
[16:32:31]    15. '120建议身高110-120'
[16:32:31]    16. '130建议身高120-130'
[16:32:31]    17. '140建议身高130-140'
[16:32:31] 🎯 找到尺码区域开始位置: 第12行 '参考分类90建议身高80-90'
[16:32:31] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[16:32:31] 🔍 处理bbox: [184, 446, 339, 467] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (261, 456)
[16:32:31] 📍 记录尺码坐标: 100 -> (261, 456)
[16:32:31] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[16:32:31] 🔍 处理bbox: [16, 488, 176, 509] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (96, 498)
[16:32:31] 📍 记录尺码坐标: 110 -> (96, 498)
[16:32:31] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[16:32:31] 🔍 处理bbox: [206, 489, 365, 507] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (285, 498)
[16:32:31] 📍 记录尺码坐标: 120 -> (285, 498)
[16:32:31] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[16:32:31] 🔍 处理bbox: [16, 530, 178, 551] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (97, 540)
[16:32:31] 📍 记录尺码坐标: 130 -> (97, 540)
[16:32:31] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[16:32:31] 🔍 处理bbox: [208, 531, 368, 549] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (288, 540)
[16:32:31] 📍 记录尺码坐标: 140 -> (288, 540)
[16:32:31] 📊 尺码提取结果: 数字=[100, 110, 120, 130, 140], 范围=100-140, 原始文本数量=5
[16:32:31] 尺码信息: {'optimized_range': '100-140', 'original_texts': ['100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [100, 110, 120, 130, 140]}
[16:32:31] 📍 已记录尺码坐标: {90: (37, 421), 100: (261, 456), 110: (96, 498), 120: (285, 498), 130: (97, 540), 140: (288, 540)}
[16:32:31] 🎯 找到颜色分类开始位置: 第7行 '颜色分类'
[16:32:31] 🎯 找到颜色分类结束位置: 第11行 '参考分类90建议身高80-90'
[16:32:31] 🔍 开始提取颜色分类: 从第8行到第10行
[16:32:31] ✅ 保留有效文本: '藏青色三件套'
[16:32:31] ✅ 保留有效文本: '焦糖色三件套'
[16:32:31] ✅ 保留有效文本: '单件短裙黑色'
[16:32:31] 🔍 检查颜色文本: '藏青色三件套' (长度: 6)
[16:32:31] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 32 ... 385]
[16:32:31] 🔍 价格检测结果: False
[16:32:31] 🔍 处理bbox: [32, 365, 123, 385] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (77, 375)
[16:32:31] 📍 记录颜色坐标: 藏青色三件套 -> (77, 375)
[16:32:31] ✅ 提取到无价格颜色: 藏青色三件套
[16:32:31] 🔍 检查颜色文本: '焦糖色三件套' (长度: 6)
[16:32:31] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [165 ... 385]
[16:32:31] 🔍 价格检测结果: False
[16:32:31] 🔍 处理bbox: [165, 365, 255, 385] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (210, 375)
[16:32:31] 📍 记录颜色坐标: 焦糖色三件套 -> (210, 375)
[16:32:31] ✅ 提取到无价格颜色: 焦糖色三件套
[16:32:31] 🔍 检查颜色文本: '单件短裙黑色' (长度: 6)
[16:32:31] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [305 ... 385]
[16:32:31] 🔍 价格检测结果: False
[16:32:31] 🔍 处理bbox: [305, 365, 394, 385] (长度: 4)
[16:32:31] ✅ 计算坐标成功: (349, 375)
[16:32:31] 📍 记录颜色坐标: 单件短裙黑色 -> (349, 375)
[16:32:31] ✅ 提取到无价格颜色: 单件短裙黑色
[16:32:31] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[16:32:31] 📍 坐标记录完成: 共记录 3 个坐标
[16:32:31] 提取到颜色分类: [{'pure_name': '藏青色三件套', 'original_text': '藏青色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 32, ..., 385], dtype=int16)}, {'pure_name': '焦糖色三件套', 'original_text': '焦糖色三件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([165, ..., 385], dtype=int16)}, {'pure_name': '单件短裙黑色', 'original_text': '单件短裙黑色', 'has_direct_price': False, 'direct_price': None, 'bbox': array([305, ..., 385], dtype=int16)}]
[16:32:31] 颜色分类数量: 3
[16:32:31] 📍 已记录颜色坐标: {'藏青色三件套': (77, 375), '焦糖色三件套': (210, 375), '单件短裙黑色': (349, 375)}
[16:32:31] 🎯 从页面提取价格信息
[16:32:31] ✅ 页面通用价格: 35.962 (来源: ￥35.962件9.9折)
[16:32:31] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[16:32:31] 🔍 商品类型分析:
[16:32:31]    颜色数量: 3
[16:32:31]    颜色直接带价格: False
[16:32:31]    页面有券前/券后价格: False
[16:32:31]    尺码带价格: False
[16:32:31] 📊 分析类型: type3_multiple_colors_no_prices
[16:32:31] 🔧 处理方案: interactive
[16:32:31] 📊 坐标对比结果（仅对比原有颜色）:
[16:32:31]    藏青色三件套: (77, 375) → (77, 375) (差异: X±0, Y±0)
[16:32:31]    ✅ 藏青色三件套 坐标无显著变化
[16:32:31]    焦糖色三件套: (210, 375) → (210, 375) (差异: X±0, Y±0)
[16:32:31]    ✅ 焦糖色三件套 坐标无显著变化
[16:32:31]    单件短裙黑色: (349, 375) → (349, 375) (差异: X±0, Y±0)
[16:32:31]    ✅ 单件短裙黑色 坐标无显著变化
[16:32:31] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[16:32:31] ✅ 坐标无变化，继续使用原始坐标
[16:32:31] 💾 保存分析结果到Excel...
[16:32:31] 找到目标商品链接在第 68 行
[16:32:31] ✅ 保存有价格颜色: 藏青色三件套 -> 69.23
[16:32:31] ✅ 保存有价格颜色: 焦糖色三件套 -> 69.23
[16:32:31] ✅ 保存有价格颜色: 单件短裙黑色 -> 35.96
[16:32:31] 找到下一个商品链接在第 79 行
[16:32:31] 清空第 69 行到第 78 行中的 0 行有内容数据，保留空行
[16:32:31] ✅ 分析结果已保存到商品7下方: 商品图片\商品SKU信息.xlsx
[16:32:31]    插入了 5 行新数据
[16:32:31] 🎉 交互式价格获取完成
[16:32:31] ==================================================
[16:32:31] ✅ 智能OCR分析完成
[16:32:31] ==================================================
[16:32:31] ✅ 详情页图片捕获完成
[16:32:31] ✅ OCR分析和Excel保存已在详情页处理中完成
[16:32:31] ✅ 商品 7 处理完成
[16:32:31] 
🎉 选中商品处理完成！成功: 1/1

