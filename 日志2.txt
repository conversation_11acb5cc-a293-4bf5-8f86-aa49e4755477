[16:37:10] 步骤13.10: 执行智能OCR分析...
[16:37:10] ==================================================
[16:37:10] 🧠 智能OCR分析开始
[16:37:10] ==================================================
[16:37:10] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[16:37:10] ✅ OCR截图已保存: 商品图片\7.jpg
[16:37:15] 正在执行OCR识别...
[16:37:18] OCR原始结果类型: <class 'list'>
[16:37:18] OCR原始结果长度: 1
[16:37:18] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[16:37:18] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[16:37:18]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[16:37:18]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000001A0833662A0>
[16:37:18]   _rand_fn: <class 'NoneType'> - None
[16:37:18]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000001A08A730FE0>
[16:37:18] 方式2成功：识别到 21 个文本
[16:37:18] 文本 0: '￥35.96- 69.23', 置信度: 0.8831
[16:37:18] OCR识别: ￥35.96- 69.23 (置信度: 0.8831)
[16:37:18] 文本 1: '请选择：颜色分类参考分类', 置信度: 0.9965
[16:37:18] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9965)
[16:37:18] 文本 2: '颜色分类', 置信度: 0.9998
[16:37:18] OCR识别: 颜色分类 (置信度: 0.9998)
[16:37:18] 文本 3: '参考分类', 置信度: 0.9999
[16:37:18] OCR识别: 参考分类 (置信度: 0.9999)
[16:37:18] 文本 4: '颜色分类', 置信度: 0.9998
[16:37:18] OCR识别: 颜色分类 (置信度: 0.9998)
[16:37:18] 文本 5: '藏青色三件套', 置信度: 0.9988
[16:37:18] OCR识别: 藏青色三件套 (置信度: 0.9988)
[16:37:18] 文本 6: '焦糖色三件套', 置信度: 0.9991
[16:37:18] OCR识别: 焦糖色三件套 (置信度: 0.9991)
[16:37:18] 文本 7: '单件短裙黑色', 置信度: 0.9992
[16:37:18] OCR识别: 单件短裙黑色 (置信度: 0.9992)
[16:37:18] 文本 8: '￥69.23', 置信度: 0.9566
[16:37:18] OCR识别: ￥69.23 (置信度: 0.9566)
[16:37:18] 文本 9: '￥69.23', 置信度: 0.9439
[16:37:18] OCR识别: ￥69.23 (置信度: 0.9439)
[16:37:18] 文本 10: '￥35.96', 置信度: 0.9431
[16:37:18] OCR识别: ￥35.96 (置信度: 0.9431)
[16:37:18] 文本 11: '参考分类', 置信度: 0.9999
[16:37:18] OCR识别: 参考分类 (置信度: 0.9999)
[16:37:18] 文本 12: '90建议身高80-90', 置信度: 0.9990
[16:37:18] OCR识别: 90建议身高80-90 (置信度: 0.9990)
[16:37:18] 文本 13: '100建议身高90-100', 置信度: 0.9984
[16:37:18] OCR识别: 100建议身高90-100 (置信度: 0.9984)
[16:37:18] 文本 14: '110建议身高100-110', 置信度: 0.9972
[16:37:18] OCR识别: 110建议身高100-110 (置信度: 0.9972)
[16:37:18] 文本 15: '120建议身高110-120', 置信度: 0.9980
[16:37:18] OCR识别: 120建议身高110-120 (置信度: 0.9980)
[16:37:18] 文本 16: '130建议身高120-130', 置信度: 0.9978
[16:37:18] OCR识别: 130建议身高120-130 (置信度: 0.9978)
[16:37:18] 文本 17: '140建议身高130-140', 置信度: 0.9984
[16:37:18] OCR识别: 140建议身高130-140 (置信度: 0.9984)
[16:37:18] 文本 18: '免费服务', 置信度: 0.9998
[16:37:18] OCR识别: 免费服务 (置信度: 0.9998)
[16:37:18] 文本 19: '退货包运费(商家赠送）', 置信度: 0.9301
[16:37:18] OCR识别: 退货包运费(商家赠送） (置信度: 0.9301)
[16:37:18] 文本 20: '一次选多款，不满意可退货包运费>', 置信度: 0.9875
[16:37:18] OCR识别: 一次选多款，不满意可退货包运费> (置信度: 0.9875)
[16:37:18] ✅ OCR识别完成，共识别到 21 个文本
[16:37:18] 💾 保存OCR结果到文件...
[16:37:18] ✅ OCR结果已保存: OCR\商品7_OCR结果.txt
[16:37:18] 🧠 开始智能分析商品信息...
[16:37:18] 🔧 开始智能拼接被分割的文本...
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '颜色分类' Y范围: 84.0-106.0, X范围: 4.0-72.0
[16:37:18]    当前文本: '颜色分类' Y范围: 121.0-144.0, X起始: 4.0
[16:37:18]    垂直间距: 15.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '颜色分类' + '颜色分类' = '颜色分类颜色分类'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '颜色分类颜色分类'
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '藏青色三件套' Y范围: 278.0-297.0, X范围: 37.0-119.0
[16:37:18]    当前文本: '￥69.23' Y范围: 293.0-312.0, X起始: 47.0
[16:37:18]    垂直间距: 4.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '藏青色三件套' + '￥69.23' = '藏青色三件套￥69.23'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '藏青色三件套￥69.23'
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '焦糖色三件套' Y范围: 278.0-297.0, X范围: 168.0-249.0
[16:37:18]    当前文本: '￥69.23' Y范围: 294.0-310.0, X起始: 186.0
[16:37:18]    垂直间距: 3.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '焦糖色三件套' + '￥69.23' = '焦糖色三件套￥69.23'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '焦糖色三件套￥69.23'
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '单件短裙黑色' Y范围: 278.0-297.0, X范围: 307.0-388.0
[16:37:18]    当前文本: '￥35.96' Y范围: 293.0-312.0, X起始: 323.0
[16:37:18]    垂直间距: 4.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '单件短裙黑色' + '￥35.96' = '单件短裙黑色￥35.96'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '单件短裙黑色￥35.96'
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '参考分类' Y范围: 322.0-348.0, X范围: 2.0-74.0
[16:37:18]    当前文本: '90建议身高80-90' Y范围: 356.0-381.0, X起始: 9.0
[16:37:18]    垂直间距: 8.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '参考分类90建议身高80-90'
[16:37:18] 🎯 找到上方紧贴文本:
[16:37:18]    上方文本: '免费服务' Y范围: 481.0-506.0, X范围: 2.0-73.0
[16:37:18]    当前文本: '退货包运费(商家赠送）' Y范围: 515.0-537.0, X起始: 28.0
[16:37:18]    垂直间距: 9.0px, X重叠: True
[16:37:18] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送）' = '免费服务退货包运费(商家赠送）'
[16:37:18] 🔗 完成向上拼接:
[16:37:18]    结果: '免费服务退货包运费(商家赠送）'
[16:37:18] 📊 拼接结果: 原始21个文本 → 拼接后15个文本
[16:37:18] 📝 拼接后的文本列表:
[16:37:18]    1. '￥35.96- 69.23'
[16:37:18]    2. '请选择：颜色分类参考分类'
[16:37:18]    3. '参考分类'
[16:37:18]    4. '颜色分类颜色分类'
[16:37:18]    5. '藏青色三件套￥69.23'
[16:37:18]    6. '焦糖色三件套￥69.23'
[16:37:18]    7. '单件短裙黑色￥35.96'
[16:37:18]    8. '参考分类90建议身高80-90'
[16:37:18]    9. '100建议身高90-100'
[16:37:18]    10. '110建议身高100-110'
[16:37:18]    11. '120建议身高110-120'
[16:37:18]    12. '130建议身高120-130'
[16:37:18]    13. '140建议身高130-140'
[16:37:18]    14. '免费服务退货包运费(商家赠送）'
[16:37:18]    15. '一次选多款，不满意可退货包运费>'
[16:37:18] 🎯 找到尺码区域开始位置: 第2行 '请选择：颜色分类参考分类'
[16:37:18] 📝 尺码区域文本无数字: '参考分类'
[16:37:18] 📝 尺码区域文本无数字: '颜色分类颜色分类'
[16:37:18] 🔧 过滤￥符号后的文本: '藏青色三件套'
[16:37:18] 📝 尺码区域文本无数字: '藏青色三件套'
[16:37:18] 🔧 过滤￥符号后的文本: '焦糖色三件套'
[16:37:18] 📝 尺码区域文本无数字: '焦糖色三件套'
[16:37:18] 🔧 过滤￥符号后的文本: '单件短裙黑色'
[16:37:18] 📝 尺码区域文本无数字: '单件短裙黑色'
[16:37:18] ✅ 提取尺码: 90 (来源: '参考分类90建议身高80-90')
[16:37:18] 🔍 处理bbox: [2, 322, 74, 348] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (38, 335)
[16:37:18] 📍 记录尺码坐标: 90 -> (38, 335)
[16:37:18] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[16:37:18] 🔍 处理bbox: [169, 357, 327, 379] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (248, 368)
[16:37:18] 📍 记录尺码坐标: 100 -> (248, 368)
[16:37:18] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[16:37:18] 🔍 处理bbox: [11, 402, 171, 420] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (91, 411)
[16:37:18] 📍 记录尺码坐标: 110 -> (91, 411)
[16:37:18] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[16:37:18] 🔍 处理bbox: [190, 400, 352, 421] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (271, 410)
[16:37:18] 📍 记录尺码坐标: 120 -> (271, 410)
[16:37:18] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[16:37:18] 🔍 处理bbox: [10, 442, 173, 463] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (91, 452)
[16:37:18] 📍 记录尺码坐标: 130 -> (91, 452)
[16:37:18] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[16:37:18] 🔍 处理bbox: [192, 442, 356, 463] (长度: 4)
[16:37:18] ✅ 计算坐标成功: (274, 452)
[16:37:18] 📍 记录尺码坐标: 140 -> (274, 452)
[16:37:18] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送）'
[16:37:18] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[16:37:18] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[16:37:18] 📍 已记录尺码坐标: {90: (38, 335), 100: (248, 368), 110: (91, 411), 120: (271, 410), 130: (91, 452), 140: (274, 452)}
[16:37:18] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类' → 提取: '参考分类'
[16:37:18] 🎯 找到颜色分类开始位置: 第1行 '请选择：颜色分类参考分类'
[16:37:18] 🎯 找到颜色分类结束位置: 第2行 '参考分类'
[16:37:18] 🔍 开始提取颜色分类: 从第2行到第1行
[16:37:18] 🎨 颜色分类提取完成: 共提取到 0 个颜色
[16:37:18] 📍 坐标记录完成: 共记录 0 个坐标
[16:37:18] 提取到颜色分类: []
[16:37:18] 颜色分类数量: 0
[16:37:18] 📍 已记录颜色坐标: {}
[16:37:18] 🎯 从页面提取价格信息
[16:37:18] ✅ 页面通用价格: 35.96 (来源: ￥35.96- 69.23)
[16:37:18] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[16:37:18] 🔍 商品类型分析:
[16:37:18]    颜色数量: 0
[16:37:18]    颜色直接带价格: False
[16:37:18]    页面有券前/券后价格: False
[16:37:18]    尺码带价格: False
[16:37:18] 📊 分析类型: type2_single_color_page_prices
[16:37:18] 🔧 处理方案: interactive
[16:37:18] 📊 智能分析结果:
[16:37:18]   优化尺码范围: 90-140
[16:37:18]   原始尺码文本: ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140']
[16:37:18]   颜色分类: []
[16:37:18]   颜色价格: {}
[16:37:18]   分析类型: type2_single_color_page_prices
[16:37:18]   处理方案: interactive
[16:37:18] 🔄 切换到交互式价格获取方案
[16:37:18] 🚀 开始交互式价格获取...
[16:37:18] 所有颜色都已有价格，无需交互
[16:37:18] 💾 保存分析结果到Excel...
[16:37:18] 找到目标商品链接在第 68 行
[16:37:18] 找到下一个商品链接在第 79 行
[16:37:18] 清空第 69 行到第 78 行中的 0 行有内容数据，保留空行
[16:37:18] ✅ 分析结果已保存到商品7下方: 商品图片\商品SKU信息.xlsx
[16:37:18]    插入了 2 行新数据
[16:37:18] ==================================================
[16:37:18] ✅ 智能OCR分析完成
[16:37:18] ==================================================
[16:37:18] ✅ 详情页图片捕获完成
[16:37:18] ✅ OCR分析和Excel保存已在详情页处理中完成
[16:37:18] ✅ 商品 7 处理完成
[16:37:18] 
🎉 选中商品处理完成！成功: 1/1

