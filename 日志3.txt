[17:05:37] 🎉 应用启动成功，等待导入Excel文件...
[17:05:38] ✅ F8全局热键已注册
[17:05:38] 正在检测连接的MTP设备...
[17:05:38] 检查设备: 下载
[17:05:38] 检查设备: 3D 对象
[17:05:38] 检查设备: 图片
[17:05:38] 检查设备: 音乐
[17:05:38] 检查设备: 桌面
[17:05:38] 检查设备: 文档
[17:05:38] 检查设备: 视频
[17:05:38] 检查设备: iQOO Z1
[17:05:38] 发现可能的手机设备: iQOO Z1
[17:05:38] ✅ 在设备 'iQOO Z1' 中找到目标路径: 内部存储设备\DCIM\Pindd\goods
[17:05:38] ✅ 检测到MTP设备: iQOO Z1
[17:05:40] 正在读取文件: 诗蝶仙.xlsx
[17:05:40] Excel文件读取成功
[17:05:40] 📊 已读取8个商品链接
[17:05:40] ✅ Excel文件已复制到: 商品图片\商品SKU信息.xlsx
[17:05:40] 📁 正在创建 8 个商品文件夹...
[17:05:40] ✅ 成功创建商品文件夹: 1-8
[17:05:40] 📦 已加载 8 个商品，可选择性执行
[17:05:40] 🔗 链接预览:
[17:05:40]   1. https://mobile.yangkeduo.com/goods2.html?ps=TibNIhg8lH
[17:05:40]   2. https://mobile.yangkeduo.com/goods.html?ps=wFRcr53KwC
[17:05:40]   3. https://mobile.yangkeduo.com/goods1.html?ps=rF3uaVr2jB
[17:05:40]   ... 还有 5 个链接
[17:05:44] 🚀 开始执行选中的商品: [1, 7]
[17:05:44] 请确保投屏软件已打开并处于可操作状态
[17:05:44] 3秒后开始执行操作...
[17:05:44] 倒计时: 3 秒
[17:05:45] 倒计时: 2 秒
[17:05:46] 倒计时: 1 秒
[17:05:47] 📱 检测连接设备品牌...
[17:05:47] 🔍 开始检测连接设备品牌...
[17:05:47] 正在检测连接的MTP设备...
[17:05:47] 检查设备: 下载
[17:05:47] 检查设备: 3D 对象
[17:05:47] 检查设备: 图片
[17:05:47] 检查设备: 音乐
[17:05:47] 检查设备: 桌面
[17:05:47] 检查设备: 文档
[17:05:47] 检查设备: 视频
[17:05:47] 检查设备: iQOO Z1
[17:05:47] 发现可能的手机设备: iQOO Z1
[17:05:47] ✅ 在设备 'iQOO Z1' 中找到目标路径: 内部存储设备\DCIM\Pindd\goods
[17:05:47] 检测到设备: iQOO Z1
[17:05:47] ✅ 检测到IQOO品牌设备，使用图片文件夹: image\IQOO
[17:05:47] ✅ 设备品牌检测完成，当前图片文件夹: image\IQOO
[17:05:47] 📱 当前设备: iQOO Z1
[17:05:47] 
==================================================
[17:05:47] 📍 开始处理商品 1 (1/2)
[17:05:47] 商品链接: https://mobile.yangkeduo.com/goods2.html?ps=TibNIhg8lH
[17:05:47] ==================================================
[17:05:47] 步骤1: 检测页面状态...
[17:05:47] 🔍 检测页面是否存在SOUSUO2.png...
[17:05:47] 找到图片 SOUSUO2.png，位置: (641, 82)
[17:05:47] ✅ 页面状态正常，SOUSUO2.png存在
[17:05:47] 步骤2: 设置剪贴板内容...
[17:05:47] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods2.html?ps=TibNIhg8lH
[17:05:48] ✅ 剪贴板内容验证成功
[17:05:48] 步骤3: 点击搜索框并输入链接...
[17:05:48] 移动到位置(480,96)并点击...
[17:05:48] 按下Ctrl+A全选...
[17:05:48] 执行AutoHotkey脚本: ctrl_a.ahk
[17:05:49] ✅ AutoHotkey脚本执行成功: ctrl_a
[17:05:50] 粘贴商品链接...
[17:05:50] 使用外部脚本软件执行真正的Ctrl+V...
[17:05:50] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[17:05:50] 方法1: 尝试AutoHotkey...
[17:05:50] 找到脚本文件: paste_v2.ahk
[17:05:50] 检查路径: AutoHotkey.exe
[17:05:50] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[17:05:50] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[17:05:50] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[17:05:50] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[17:05:50] 执行AutoHotkey脚本: paste_v2.ahk
[17:05:52] AutoHotkey返回码: 0
[17:05:52] ✅ AutoHotkey执行成功
[17:05:52] ✅ Ctrl+V操作执行成功
[17:05:55] 步骤4: 点击搜索按钮...
[17:05:55] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[17:05:55] 找到图片 SOUSUO2.png，位置: (641, 82)
[17:05:55] 点击位置: (664, 97)
[17:05:55] 已点击图片: SOUSUO2.png
[17:05:55] 步骤4.5: 检测是否出现验证码...
[17:05:55] 等待2秒后检测验证码...
[17:05:57] 查找图片 YANZHENGMA.png 时出错: 
[17:05:57] ✅ 未检测到验证码，继续正常流程
[17:05:57] 步骤5: 等待3秒进入商品页面...
[17:06:00] 步骤6: 执行从右往左的滑动操作...
[17:06:00] 开始执行从右往左的滑动操作...
[17:06:00] 📍 起始位置: (630, 185)
[17:06:00] 📍 结束位置: (284, 185)
[17:06:00] 📏 滑动距离: 346 像素
[17:06:00] ⏱️ 滑动时长: 0.3 秒
[17:06:00] 移动到起始位置(630, 185)
[17:06:00] 按住左键从(630, 185)拖拽到(284, 185)
[17:06:01] 开始滑动，时长0.3秒...
[17:06:02] 释放左键，滑动操作完成
[17:06:02] ✅ 从右往左的滑动操作完成
[17:06:02] 步骤7: 移动到位置(470,185)...
[17:06:02] 已移动到位置(470,185)
[17:06:02] 步骤8: 执行鼠标点击和长按操作...
[17:06:02] 点击鼠标左键一次
[17:06:03] 在位置(475, 323)长按鼠标左键0.5秒
[17:06:04] 鼠标操作完成
[17:06:05] 步骤9: 查找并点击保存按钮...
[17:06:05] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[17:06:05] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:06:05] 查找或点击图片 BAOCUN1.png 时出错: 
[17:06:05] 未找到图片 BAOCUN1.png，等待5秒后重试...
[17:06:10] 尝试查找图片 BAOCUN1.png (第2次/共3次)
[17:06:10] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:06:10] 找到图片 BAOCUN1.png，位置: (450, 808)
[17:06:11] 移动到图片上，等待0.5秒...
[17:06:11] 已点击图片: BAOCUN1.png
[17:06:11] 步骤10: 检测保存状态...
[17:06:11] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[17:06:11] 最多检测 80 次，每隔 0.2 秒检测一次
[17:06:11] 第 1/80 次检测...
[17:06:12] 第 2/80 次检测...
[17:06:12] 第 3/80 次检测...
[17:06:12] 第 4/80 次检测...
[17:06:12] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 507)
[17:06:12] ✅ 检测到保存中状态，等待保存完成...
[17:06:12] 等待5秒让图片完全保存到手机...
[17:06:17] 步骤11: 复制手机图片到商品 1 文件夹...
[17:06:17] 第 1/3 次尝试复制图片...
[17:06:17] 开始从MTP设备复制图片到商品 1 文件夹...
[17:06:17] 正在使用Windows Shell API查找MTP设备...
[17:06:17] 找到设备: iQOO Z1
[17:06:17] 进入文件夹: 内部存储设备
[17:06:17] 进入文件夹: DCIM
[17:06:17] 进入文件夹: Pindd
[17:06:17] 进入文件夹: goods
[17:06:17] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[17:06:17] 目标路径: F:\自动捕获数据\商品图片\1
[17:06:17] 复制文件: 1755594371315--927662479.jpg
[17:06:18] ✅ 成功复制: 1755594371315--927662479.jpg
[17:06:18] 复制文件: 1755594371397--251446317.jpg
[17:06:18] ✅ 成功复制: 1755594371397--251446317.jpg
[17:06:18] 复制文件: 1755594371447-66167728.jpg
[17:06:18] ✅ 成功复制: 1755594371447-66167728.jpg
[17:06:18] 复制文件: 1755594371519-69371956.jpg
[17:06:18] ✅ 成功复制: 1755594371519-69371956.jpg
[17:06:18] 复制文件: 1755594371565--1253505950.jpg
[17:06:18] ✅ 成功复制: 1755594371565--1253505950.jpg
[17:06:18] 复制文件: 1755594371614--1876317036.jpg
[17:06:18] ✅ 成功复制: 1755594371614--1876317036.jpg
[17:06:18] 复制文件: 1755594371664-1361876500.jpg
[17:06:18] ✅ 成功复制: 1755594371664-1361876500.jpg
[17:06:18] 复制文件: 1755594371710--239472331.jpg
[17:06:18] ✅ 成功复制: 1755594371710--239472331.jpg
[17:06:18] 复制文件: 1755594371760-1753201394.jpg
[17:06:18] ✅ 成功复制: 1755594371760-1753201394.jpg
[17:06:18] 复制文件: 1755594371809--221335611.jpg
[17:06:18] ✅ 成功复制: 1755594371809--221335611.jpg
[17:06:18] 复制文件: 1755594371875-1849459058.jpg
[17:06:18] ✅ 成功复制: 1755594371875-1849459058.jpg
[17:06:18] 复制文件: 1755594371939-745988674.jpg
[17:06:18] ✅ 成功复制: 1755594371939-745988674.jpg
[17:06:18] 成功复制 12 个文件
[17:06:18] ✅ 成功复制 12 个图片到商品 1 文件夹
[17:06:18] 复制的文件:
[17:06:18]   - 1755594371315--927662479.jpg
[17:06:18]   - 1755594371397--251446317.jpg
[17:06:18]   - 1755594371447-66167728.jpg
[17:06:18]   - 1755594371519-69371956.jpg
[17:06:18]   - 1755594371565--1253505950.jpg
[17:06:18]   - 1755594371614--1876317036.jpg
[17:06:18]   - 1755594371664-1361876500.jpg
[17:06:18]   - 1755594371710--239472331.jpg
[17:06:18]   - 1755594371760-1753201394.jpg
[17:06:18]   - 1755594371809--221335611.jpg
[17:06:18]   - 1755594371875-1849459058.jpg
[17:06:18]   - 1755594371939-745988674.jpg
[17:06:18] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[17:06:18] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[17:06:18] ✅ 图片复制完成
[17:06:18] 步骤12: 删除手机上的原文件...
[17:06:18] 开始删除手机文件操作...
[17:06:18] 查找并点击GOODS.png...
[17:06:18] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[17:06:18] 找到图片 GOODS.png，位置: (790, 997)
[17:06:18] 点击位置: (815, 1006)
[17:06:19] 已点击图片: GOODS.png
[17:06:20] 使用AutoHotkey执行删除操作...
[17:06:20] 执行AutoHotkey脚本: delete_files.ahk
[17:06:23] ✅ AutoHotkey脚本执行成功: delete_files
[17:06:23] ✅ 删除操作执行成功
[17:06:23] ✅ 手机文件删除完成
[17:06:23] 步骤13: 开始处理主图...
[17:06:23] 步骤14: 开始第二轮操作（详情页）...
[17:06:23] 处理主图文件夹: 商品图片\1
[17:06:23] === 开始详情页图片捕获流程 ===
[17:06:23] 找到 12 张图片
[17:06:23] 步骤13.1: 移动到(475,230)并点击右键...
[17:06:23] 删除多余图片: 1755594371565--1253505950.jpg
[17:06:23] 删除多余图片: 1755594371614--1876317036.jpg
[17:06:23] 删除多余图片: 1755594371664-1361876500.jpg
[17:06:23] 删除多余图片: 1755594371710--239472331.jpg
[17:06:23] 删除多余图片: 1755594371760-1753201394.jpg
[17:06:23] 删除多余图片: 1755594371809--221335611.jpg
[17:06:23] 删除多余图片: 1755594371875-1849459058.jpg
[17:06:23] 删除多余图片: 1755594371939-745988674.jpg
[17:06:23] 重命名: 1755594371315--927662479.jpg → 主图1.jpg
[17:06:23] 重命名: 1755594371397--251446317.jpg → 主图2.jpg
[17:06:23] 重命名: 1755594371447-66167728.jpg → 主图3.jpg
[17:06:23] 重命名: 1755594371519-69371956.jpg → 主图4.jpg
[17:06:23] ✅ 主图处理完成
[17:06:25] 步骤13.2: 使用AutoHotkey向下滚动40次...
[17:06:25] 执行AutoHotkey脚本: scroll_down.ahk
[17:06:29] ✅ AutoHotkey脚本执行成功: scroll_down
[17:06:29] ✅ AutoHotkey滚动执行成功
[17:06:29] 步骤13.3: 移动到(477,300)并点击左键...
[17:06:30] ✅ 点击操作完成
[17:06:30] 步骤13.4: 移动到(480,495)并长按0.5秒...
[17:06:31] ✅ 长按操作完成
[17:06:32] 步骤13.5: 查找并点击保存按钮...
[17:06:32] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[17:06:32] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:06:32] 查找或点击图片 BAOCUN1.png 时出错: 
[17:06:32] 未找到图片 BAOCUN1.png，等待5秒后重试...
[17:06:37] 尝试查找图片 BAOCUN1.png (第2次/共3次)
[17:06:37] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:06:38] 查找或点击图片 BAOCUN1.png 时出错: 
[17:06:38] 未找到图片 BAOCUN1.png，等待5秒后重试...
[17:06:43] 尝试查找图片 BAOCUN1.png (第3次/共3次)
[17:06:43] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:06:43] 找到图片 BAOCUN1.png，位置: (451, 808)
[17:06:43] 移动到图片上，等待0.5秒...
[17:06:44] 已点击图片: BAOCUN1.png
[17:06:44] 步骤13.6: 检测详情页保存状态...
[17:06:44] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[17:06:44] 最多检测 80 次，每隔 0.2 秒检测一次
[17:06:44] 第 1/80 次检测...
[17:06:44] 第 2/80 次检测...
[17:06:44] 第 3/80 次检测...
[17:06:44] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 509)
[17:06:44] ✅ 检测到详情页保存中状态，等待保存完成...
[17:06:49] 步骤13.7: 复制详情页图片...
[17:06:49] 第 1/3 次尝试复制详情页图片...
[17:06:49] 开始从MTP设备复制图片到商品 1 文件夹...
[17:06:49] 正在使用Windows Shell API查找MTP设备...
[17:06:49] 找到设备: iQOO Z1
[17:06:49] 进入文件夹: 内部存储设备
[17:06:49] 进入文件夹: DCIM
[17:06:49] 进入文件夹: Pindd
[17:06:49] 进入文件夹: goods
[17:06:49] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[17:06:49] 目标路径: F:\自动捕获数据\商品图片\1
[17:06:49] 复制文件: 1755594403638--320482028.jpg
[17:06:50] ✅ 成功复制: 1755594403638--320482028.jpg
[17:06:50] 复制文件: 1755594403718--1202363698.jpg
[17:06:50] ✅ 成功复制: 1755594403718--1202363698.jpg
[17:06:50] 复制文件: 1755594403767--2001003629.jpg
[17:06:50] ✅ 成功复制: 1755594403767--2001003629.jpg
[17:06:50] 复制文件: 1755594403827--465100475.jpg
[17:06:50] ✅ 成功复制: 1755594403827--465100475.jpg
[17:06:50] 复制文件: 1755594403876-176918590.jpg
[17:06:50] ✅ 成功复制: 1755594403876-176918590.jpg
[17:06:50] 复制文件: 1755594403925-241234043.jpg
[17:06:50] ✅ 成功复制: 1755594403925-241234043.jpg
[17:06:50] 复制文件: 1755594403990--37124809.jpg
[17:06:50] ✅ 成功复制: 1755594403990--37124809.jpg
[17:06:50] 复制文件: 1755594404038-657773704.jpg
[17:06:50] ✅ 成功复制: 1755594404038-657773704.jpg
[17:06:50] 复制文件: 1755594404093-646697714.jpg
[17:06:50] ✅ 成功复制: 1755594404093-646697714.jpg
[17:06:50] 复制文件: 1755594404159-614008898.jpg
[17:06:50] ✅ 成功复制: 1755594404159-614008898.jpg
[17:06:50] 复制文件: 1755594404207-1342364973.jpg
[17:06:50] ✅ 成功复制: 1755594404207-1342364973.jpg
[17:06:50] 成功复制 11 个文件
[17:06:50] ✅ 成功复制 11 个图片到商品 1 文件夹
[17:06:50] 复制的文件:
[17:06:50]   - 1755594403638--320482028.jpg
[17:06:50]   - 1755594403718--1202363698.jpg
[17:06:50]   - 1755594403767--2001003629.jpg
[17:06:50]   - 1755594403827--465100475.jpg
[17:06:50]   - 1755594403876-176918590.jpg
[17:06:50]   - 1755594403925-241234043.jpg
[17:06:50]   - 1755594403990--37124809.jpg
[17:06:50]   - 1755594404038-657773704.jpg
[17:06:50]   - 1755594404093-646697714.jpg
[17:06:50]   - 1755594404159-614008898.jpg
[17:06:50]   - 1755594404207-1342364973.jpg
[17:06:50] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[17:06:50] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[17:06:50] ✅ 详情页图片复制完成
[17:06:50] 步骤13.8: 删除手机上的详情页图片...
[17:06:50] 开始删除手机文件操作...
[17:06:50] 查找并点击GOODS.png...
[17:06:50] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[17:06:50] 找到图片 GOODS.png，位置: (790, 997)
[17:06:50] 点击位置: (815, 1006)
[17:06:50] 已点击图片: GOODS.png
[17:06:51] 使用AutoHotkey执行删除操作...
[17:06:51] 执行AutoHotkey脚本: delete_files.ahk
[17:06:55] ✅ AutoHotkey脚本执行成功: delete_files
[17:06:55] ✅ 删除操作执行成功
[17:06:55] ✅ 手机详情页文件删除完成
[17:06:55] 步骤13.8: 处理详情页图片...
[17:06:55] 处理详情页图片文件夹: 商品图片\1
[17:06:55] 跳过已处理的主图: 主图1.jpg
[17:06:55] 跳过已处理的主图: 主图2.jpg
[17:06:55] 跳过已处理的主图: 主图3.jpg
[17:06:55] 跳过已处理的主图: 主图4.jpg
[17:06:55] 找到 11 张详情页图片
[17:06:55] 重命名详情页图片: 1755594403638--320482028.jpg → 1.jpg
[17:06:55] 重命名详情页图片: 1755594403718--1202363698.jpg → 2.jpg
[17:06:55] 重命名详情页图片: 1755594403767--2001003629.jpg → 3.jpg
[17:06:55] 重命名详情页图片: 1755594403827--465100475.jpg → 4.jpg
[17:06:55] 重命名详情页图片: 1755594403876-176918590.jpg → 5.jpg
[17:06:55] 重命名详情页图片: 1755594403925-241234043.jpg → 6.jpg
[17:06:55] 重命名详情页图片: 1755594403990--37124809.jpg → 7.jpg
[17:06:55] 重命名详情页图片: 1755594404038-657773704.jpg → 8.jpg
[17:06:55] 重命名详情页图片: 1755594404093-646697714.jpg → 9.jpg
[17:06:55] 重命名详情页图片: 1755594404159-614008898.jpg → 10.jpg
[17:06:55] 重命名详情页图片: 1755594404207-1342364973.jpg → 11.jpg
[17:06:55] ✅ 详情页图片处理完成
[17:06:55] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[17:06:55] ✅ 详情页图片处理完成
[17:06:55] 步骤13.9: 执行最后的鼠标操作...
[17:06:55] 开始执行最后的鼠标操作序列...
[17:06:55] 移动到位置(475,125)...
[17:06:56] 点击右键...
[17:06:56] 等待1秒...
[17:06:57] 移动到位置(670,940)...
[17:06:58] 点击左键...
[17:06:58] 等待2.5秒让页面加载完成...
[17:07:00] ✅ 最后的鼠标操作序列完成
[17:07:00] 步骤13.10: 执行智能OCR分析...
[17:07:00] ==================================================
[17:07:00] 🧠 智能OCR分析开始
[17:07:00] ==================================================
[17:07:00] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[17:07:00] ✅ OCR截图已保存: 商品图片\1.jpg
[17:07:06] 正在执行OCR识别...
[17:07:08] OCR原始结果类型: <class 'list'>
[17:07:08] OCR原始结果长度: 1
[17:07:08] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:07:08] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[17:07:08]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[17:07:08]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002B9623F32C0>
[17:07:08]   _rand_fn: <class 'NoneType'> - None
[17:07:08]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002B96A3DB9B0>
[17:07:08] 方式2成功：识别到 21 个文本
[17:07:08] 文本 0: '¥28 - 69.9', 置信度: 0.8566
[17:07:08] OCR识别: ¥28 - 69.9 (置信度: 0.8566)
[17:07:08] 文本 1: '1', 置信度: 0.9972
[17:07:08] OCR识别: 1 (置信度: 0.9972)
[17:07:08] 文本 2: '+', 置信度: 0.9954
[17:07:08] OCR识别: + (置信度: 0.9954)
[17:07:08] 文本 3: '同款热销', 置信度: 0.9994
[17:07:08] OCR识别: 同款热销 (置信度: 0.9994)
[17:07:08] 文本 4: '请选择：颜色分类参考分类', 置信度: 0.9904
[17:07:08] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9904)
[17:07:08] 文本 5: '颜色分类', 置信度: 0.9998
[17:07:08] OCR识别: 颜色分类 (置信度: 0.9998)
[17:07:08] 文本 6: '参考分类', 置信度: 0.9999
[17:07:08] OCR识别: 参考分类 (置信度: 0.9999)
[17:07:08] 文本 7: '颜色分类', 置信度: 0.9999
[17:07:08] OCR识别: 颜色分类 (置信度: 0.9999)
[17:07:08] 文本 8: '快要抢光', 置信度: 0.9977
[17:07:08] OCR识别: 快要抢光 (置信度: 0.9977)
[17:07:08] 文本 9: '藏青色拼接两件', 置信度: 0.9981
[17:07:08] OCR识别: 藏青色拼接两件 (置信度: 0.9981)
[17:07:08] 文本 10: '套', 置信度: 0.9984
[17:07:08] OCR识别: 套 (置信度: 0.9984)
[17:07:08] 文本 11: '黑色印花T恤【单】', 置信度: 0.9937
[17:07:08] OCR识别: 黑色印花T恤【单】 (置信度: 0.9937)
[17:07:08] 文本 12: '参考分类', 置信度: 0.9999
[17:07:08] OCR识别: 参考分类 (置信度: 0.9999)
[17:07:08] 文本 13: '110码适合身高1米05左右', 置信度: 0.9992
[17:07:08] OCR识别: 110码适合身高1米05左右 (置信度: 0.9992)
[17:07:08] 文本 14: '120码适合身高1米15左右', 置信度: 0.9987
[17:07:08] OCR识别: 120码适合身高1米15左右 (置信度: 0.9987)
[17:07:08] 文本 15: '130码适合身高1米25左右', 置信度: 0.9977
[17:07:08] OCR识别: 130码适合身高1米25左右 (置信度: 0.9977)
[17:07:08] 文本 16: '140码适合身高1米35左右', 置信度: 0.9994
[17:07:08] OCR识别: 140码适合身高1米35左右 (置信度: 0.9994)
[17:07:08] 文本 17: '150码适合身高1米45左右', 置信度: 0.9991
[17:07:08] OCR识别: 150码适合身高1米45左右 (置信度: 0.9991)
[17:07:08] 文本 18: '160码适合身高1米5左右', 置信度: 0.9994
[17:07:08] OCR识别: 160码适合身高1米5左右 (置信度: 0.9994)
[17:07:08] 文本 19: '170码适合身高1米58左右', 置信度: 0.9993
[17:07:08] OCR识别: 170码适合身高1米58左右 (置信度: 0.9993)
[17:07:08] 文本 20: '免费服务', 置信度: 0.9999
[17:07:08] OCR识别: 免费服务 (置信度: 0.9999)
[17:07:08] ✅ OCR识别完成，共识别到 21 个文本
[17:07:08] 💾 保存OCR结果到文件...
[17:07:08] ✅ OCR结果已保存: OCR\商品1_OCR结果.txt
[17:07:08] 🧠 开始智能分析商品信息...
[17:07:08] 🔧 开始智能拼接被分割的文本...
[17:07:08] 🎯 找到上方紧贴文本:
[17:07:08]    上方文本: '¥28 - 69.9' Y范围: 9.0-38.0, X范围: 2.0-104.0
[17:07:08]    当前文本: '同款热销' Y范围: 37.0-62.0, X起始: 5.0
[17:07:08]    垂直间距: 1.0px, X重叠: True
[17:07:08] ✅ 向上拼接: '¥28 - 69.9' + '同款热销' = '¥28 - 69.9同款热销'
[17:07:08] 🔗 完成向上拼接:
[17:07:08]    结果: '¥28 - 69.9同款热销'
[17:07:08] 🎯 找到上方紧贴文本:
[17:07:08]    上方文本: '颜色分类' Y范围: 106.0-133.0, X范围: 3.0-73.0
[17:07:08]    当前文本: '颜色分类' Y范围: 144.0-169.0, X起始: 2.0
[17:07:08]    垂直间距: 11.0px, X重叠: True
[17:07:08] ✅ 向上拼接: '颜色分类' + '颜色分类' = '颜色分类颜色分类'
[17:07:08] 🔗 完成向上拼接:
[17:07:08]    结果: '颜色分类颜色分类'
[17:07:08] 🎯 找到上方紧贴文本:
[17:07:08]    上方文本: '颜色分类' Y范围: 144.0-169.0, X范围: 2.0-74.0
[17:07:08]    当前文本: '快要抢光' Y范围: 172.0-195.0, X起始: 4.0
[17:07:08]    垂直间距: 3.0px, X重叠: True
[17:07:08] ✅ 向上拼接: '颜色分类' + '快要抢光' = '颜色分类快要抢光'
[17:07:08] 🔗 完成向上拼接:
[17:07:08]    结果: '颜色分类快要抢光'
[17:07:08] 🎯 找到上方紧贴文本:
[17:07:08]    上方文本: '藏青色拼接两件' Y范围: 302.0-320.0, X范围: 26.0-127.0
[17:07:08]    当前文本: '套' Y范围: 316.0-337.0, X起始: 61.0
[17:07:08]    垂直间距: 4.0px, X重叠: True
[17:07:08] ✅ 向上拼接: '藏青色拼接两件' + '套' = '藏青色拼接两件套'
[17:07:08] 🔗 完成向上拼接:
[17:07:08]    结果: '藏青色拼接两件套'
[17:07:08] 🎯 找到上方紧贴文本:
[17:07:08]    上方文本: '藏青色拼接两件' Y范围: 302.0-320.0, X范围: 26.0-127.0
[17:07:08]    当前文本: '黑色印花T恤【单】' Y范围: 308.0-330.0, X起始: 149.0
[17:07:08]    垂直间距: 12.0px, X重叠: True
[17:07:08] ✅ 向上拼接: '藏青色拼接两件' + '黑色印花T恤【单】' = '藏青色拼接两件黑色印花T恤【单】'
[17:07:08] 🔗 完成向上拼接:
[17:07:08]    结果: '藏青色拼接两件黑色印花T恤【单】'
[17:07:08] 📊 拼接结果: 原始21个文本 → 拼接后18个文本
[17:07:08] 📝 拼接后的文本列表:
[17:07:08]    1. '1'
[17:07:08]    2. '+'
[17:07:08]    3. '¥28 - 69.9同款热销'
[17:07:08]    4. '请选择：颜色分类参考分类'
[17:07:08]    5. '参考分类'
[17:07:08]    6. '颜色分类颜色分类'
[17:07:08]    7. '颜色分类快要抢光'
[17:07:08]    8. '藏青色拼接两件套'
[17:07:08]    9. '藏青色拼接两件黑色印花T恤【单】'
[17:07:08]    10. '参考分类'
[17:07:08]    11. '110码适合身高1米05左右'
[17:07:08]    12. '120码适合身高1米15左右'
[17:07:08]    13. '130码适合身高1米25左右'
[17:07:08]    14. '140码适合身高1米35左右'
[17:07:08]    15. '150码适合身高1米45左右'
[17:07:08]    16. '160码适合身高1米5左右'
[17:07:08]    17. '170码适合身高1米58左右'
[17:07:08]    18. '免费服务'
[17:07:08] 🎯 找到尺码区域开始位置: 第4行 '请选择：颜色分类参考分类'
[17:07:08] 📝 尺码区域文本无数字: '参考分类'
[17:07:08] 📝 尺码区域文本无数字: '颜色分类颜色分类'
[17:07:08] 📝 尺码区域文本无数字: '颜色分类快要抢光'
[17:07:08] 📝 尺码区域文本无数字: '藏青色拼接两件套'
[17:07:08] 📝 尺码区域文本无数字: '藏青色拼接两件黑色印花T恤【单】'
[17:07:08] 📝 尺码区域文本无数字: '参考分类'
[17:07:08] ✅ 提取尺码: 110 (来源: '110码适合身高1米05左右')
[17:07:08] 🔍 处理bbox: [10, 384, 201, 402] (长度: 4)
[17:07:08] ✅ 计算坐标成功: (105, 393)
[17:07:08] 📍 记录尺码坐标: 110 -> (105, 393)
[17:07:08] ✅ 提取尺码: 120 (来源: '120码适合身高1米15左右')
[17:07:08] 🔍 处理bbox: [9, 424, 202, 446] (长度: 4)
[17:07:08] ✅ 计算坐标成功: (105, 435)
[17:07:08] 📍 记录尺码坐标: 120 -> (105, 435)
[17:07:08] ✅ 提取尺码: 130 (来源: '130码适合身高1米25左右')
[17:07:08] 🔍 处理bbox: [11, 467, 202, 486] (长度: 4)
[17:07:08] ✅ 计算坐标成功: (106, 476)
[17:07:08] 📍 记录尺码坐标: 130 -> (106, 476)
[17:07:08] ✅ 提取尺码: 140 (来源: '140码适合身高1米35左右')
[17:07:08] 🔍 处理bbox: [11, 509, 203, 528] (长度: 4)
[17:07:08] ✅ 计算坐标成功: (107, 518)
[17:07:08] 📍 记录尺码坐标: 140 -> (107, 518)
[17:07:08] ✅ 提取尺码: 150 (来源: '150码适合身高1米45左右')
[17:07:08] 🔍 处理bbox: [11, 551, 203, 569] (长度: 4)
[17:07:08] ✅ 计算坐标成功: (107, 560)
[17:07:08] 📍 记录尺码坐标: 150 -> (107, 560)
[17:07:09] ✅ 提取尺码: 160 (来源: '160码适合身高1米5左右')
[17:07:09] 🔍 处理bbox: [10, 591, 195, 612] (长度: 4)
[17:07:09] ✅ 计算坐标成功: (102, 601)
[17:07:09] 📍 记录尺码坐标: 160 -> (102, 601)
[17:07:09] ✅ 提取尺码: 170 (来源: '170码适合身高1米58左右')
[17:07:09] 🔍 处理bbox: [212, 588, 410, 616] (长度: 4)
[17:07:09] ✅ 计算坐标成功: (311, 602)
[17:07:09] 📍 记录尺码坐标: 170 -> (311, 602)
[17:07:09] 🛑 遇到停止关键词，结束尺码提取: '免费服务'
[17:07:09] 📊 尺码提取结果: 数字=[110, 120, 130, 140, 150, 160, 170], 范围=110-170, 原始文本数量=7
[17:07:09] 尺码信息: {'optimized_range': '110-170', 'original_texts': ['110码适合身高1米05左右', '120码适合身高1米15左右', '130码适合身高1米25左右', '140码适合身高1米35左右', '150码适合身高1米45左右', '160码适合身高1米5左右', '170码适合身高1米58左右'], 'size_numbers': [110, 120, 130, 140, 150, 160, 170]}
[17:07:09] 📍 已记录尺码坐标: {110: (105, 393), 120: (105, 435), 130: (106, 476), 140: (107, 518), 150: (107, 560), 160: (102, 601), 170: (311, 602)}
[17:07:09] 🔍 检测到重复'颜色分类'文本(2次)，启用新格式过滤模式
[17:07:09] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类' → 提取: '参考分类'
[17:07:09] 🔍 记录颜色分类位置: 第3行 '请选择：颜色分类参考分类'
[17:07:09] 🔍 发现颜色分类嵌入格式: '颜色分类颜色分类' → 提取: '颜色分类'
[17:07:09] 🎯 发现嵌入颜色分类: '颜色分类' (来源: '颜色分类颜色分类')
[17:07:09] 🔍 记录颜色分类位置: 第5行 '颜色分类颜色分类'
[17:07:09] 🔍 发现颜色分类嵌入格式: '颜色分类快要抢光' → 提取: '快要抢光'
[17:07:09] ⚠️ 嵌入颜色清理后为空: '快要抢光' (来源: '颜色分类快要抢光')
[17:07:09] 🔍 记录颜色分类位置: 第6行 '颜色分类快要抢光'
[17:07:09] 🎯 选择最后一个颜色分类作为开始位置: 第6行
[17:07:09] 🎯 找到颜色分类结束位置: 第9行 '参考分类'
[17:07:09] 🔍 开始提取颜色分类: 从第7行到第8行
[17:07:09] ✅ 保留有效文本: '藏青色拼接两件套'
[17:07:09] ✅ 保留有效文本: '藏青色拼接两件黑色印花T恤【单】'
[17:07:09] ✅ 添加嵌入颜色分类: '颜色分类'
[17:07:09] 🔍 新格式过滤完成: 从颜色分类区间提取到 3 个有效文本
[17:07:09]    1. '藏青色拼接两件套'
[17:07:09]    2. '藏青色拼接两件黑色印花T恤【单】'
[17:07:09]    3. '颜色分类'
[17:07:09] 🔍 检查颜色文本: '藏青色拼接两件套' (长度: 8)
[17:07:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 26 ... 320]
[17:07:09] 🔍 价格检测结果: False
[17:07:09] 🔍 处理bbox: [26, 302, 127, 320] (长度: 4)
[17:07:09] ✅ 计算坐标成功: (76, 311)
[17:07:09] 📍 记录颜色坐标: 藏青色拼接两件套 -> (76, 311)
[17:07:09] ✅ 提取到无价格颜色: 藏青色拼接两件套
[17:07:09] 🔍 检查颜色文本: '藏青色拼接两件黑色印花T恤【单】' (长度: 16)
[17:07:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 26 ... 320]
[17:07:09] 🔍 价格检测结果: False
[17:07:09] 🔍 处理bbox: [26, 302, 127, 320] (长度: 4)
[17:07:09] ✅ 计算坐标成功: (76, 311)
[17:07:09] 📍 记录颜色坐标: 藏青色拼接两件黑色印花T恤【单】 -> (76, 311)
[17:07:09] ✅ 提取到无价格颜色: 藏青色拼接两件黑色印花T恤【单】
[17:07:09] 🔍 检查颜色文本: '颜色分类' (长度: 4)
[17:07:09] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  3 ... 133]
[17:07:09] 🔍 价格检测结果: False
[17:07:09] 🔍 处理bbox: [3, 106, 73, 133] (长度: 4)
[17:07:09] ✅ 计算坐标成功: (38, 119)
[17:07:09] 📍 记录颜色坐标: 颜色分类 -> (38, 119)
[17:07:09] ✅ 提取到无价格颜色: 颜色分类
[17:07:09] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[17:07:09] 📍 坐标记录完成: 共记录 3 个坐标
[17:07:09] 提取到颜色分类: [{'pure_name': '藏青色拼接两件套', 'original_text': '藏青色拼接两件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 26, ..., 320], dtype=int16)}, {'pure_name': '藏青色拼接两件黑色印花T恤【单】', 'original_text': '藏青色拼接两件黑色印花T恤【单】', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 26, ..., 320], dtype=int16)}, {'pure_name': '颜色分类', 'original_text': '颜色分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  3, ..., 133], dtype=int16)}]
[17:07:09] 颜色分类数量: 3
[17:07:09] 📍 已记录颜色坐标: {'藏青色拼接两件套': (76, 311), '藏青色拼接两件黑色印花T恤【单】': (76, 311), '颜色分类': (38, 119)}
[17:07:09] 🎯 从页面提取价格信息
[17:07:09] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[17:07:09] 🔍 商品类型分析:
[17:07:09]    颜色数量: 3
[17:07:09]    颜色直接带价格: False
[17:07:09]    页面有券前/券后价格: False
[17:07:09]    尺码带价格: False
[17:07:09] 📊 分析类型: type3_multiple_colors_no_prices
[17:07:09] 🔧 处理方案: interactive
[17:07:09] 📊 智能分析结果:
[17:07:09]   优化尺码范围: 110-170
[17:07:09]   原始尺码文本: ['110码适合身高1米05左右', '120码适合身高1米15左右', '130码适合身高1米25左右', '140码适合身高1米35左右', '150码适合身高1米45左右', '160码适合身高1米5左右', '170码适合身高1米58左右']
[17:07:09]   颜色分类: ['藏青色拼接两件套', '藏青色拼接两件黑色印花T恤【单】', '颜色分类']
[17:07:09]   颜色价格: {}
[17:07:09]   分析类型: type3_multiple_colors_no_prices
[17:07:09]   处理方案: interactive
[17:07:09] 🔄 切换到交互式价格获取方案
[17:07:09] 🚀 开始交互式价格获取...
[17:07:09] 需要交互获取价格的颜色: ['藏青色拼接两件套', '藏青色拼接两件黑色印花T恤【单】', '颜色分类']
[17:07:09] 📏 尺码选择策略: 从7个尺码中选择中间值 140
[17:07:09]    完整尺码列表: [110, 120, 130, 140, 150, 160, 170]
[17:07:09]    避免最小码: 110，避免最大码: 170
[17:07:09] 选择中间尺码: 140
[17:07:09] 🔍 检查尺码坐标记录: {110: (105, 393), 120: (105, 435), 130: (106, 476), 140: (107, 518), 150: (107, 560), 160: (102, 601), 170: (311, 602)}
[17:07:09] 🔍 查找尺码: 140
[17:07:09] 📍 使用记录的尺码坐标: 140
[17:07:09]    相对坐标: (107, 518)
[17:07:09]    绝对坐标: (377, 748)
[17:07:09] 🎯 移动到尺码 140 坐标: (377, 748)
[17:07:10] 🎯 点击尺码 140
[17:07:12] 🎯 检测到多颜色商品，需要依次点击 3 个颜色
[17:07:12] 🎨 处理颜色 1/3: 藏青色拼接两件套
[17:07:12] 📍 使用记录的坐标: 藏青色拼接两件套
[17:07:12]    相对坐标: (76, 311)
[17:07:12]    绝对坐标: (346, 541)
[17:07:12] 🎯 移动到颜色 藏青色拼接两件套 坐标: (346, 541)
[17:07:13] 🎯 点击颜色 藏青色拼接两件套
[17:07:15] 📸 截取颜色 藏青色拼接两件套 更新后的页面...
[17:07:15] ✅ 价格OCR截图已保存: 商品图片\1-1.jpg
[17:07:17] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:07:17] 字典格式价格OCR识别到 21 个文本
[17:07:17] 价格OCR识别: 限量低价￥69.9￥100 (置信度: 0.905)
[17:07:17] 价格OCR识别: 1 (置信度: 0.944)
[17:07:17] 价格OCR识别: + (置信度: 0.996)
[17:07:17] 价格OCR识别: 同款热销即将恢复100元 (置信度: 0.999)
[17:07:17] 价格OCR识别: 已选：藏青色拼接两件套140码 适合身高1米3… (置信度: 0.955)
[17:07:17] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:17] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:17] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:17] 价格OCR识别: 快要抢光 (置信度: 0.998)
[17:07:17] 价格OCR识别: 藏青色拼接两件 (置信度: 0.998)
[17:07:17] 价格OCR识别: 套 (置信度: 1.000)
[17:07:17] 价格OCR识别: 黑色印花T恤【单】 (置信度: 0.992)
[17:07:17] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:17] 价格OCR识别: 110码适合身高1米05左右 (置信度: 0.999)
[17:07:17] 价格OCR识别: 120码适合身高1米15左右 (置信度: 0.997)
[17:07:17] 价格OCR识别: 130码适合身高1米25左右 (置信度: 0.999)
[17:07:17] 价格OCR识别: 140码适合身高1米35左右 (置信度: 1.000)
[17:07:17] 价格OCR识别: 150码适合身高1米45左右 (置信度: 0.997)
[17:07:17] 价格OCR识别: 160码适合身高1米5左右 (置信度: 1.000)
[17:07:17] 价格OCR识别: 170码适合身高1米58左右 (置信度: 1.000)
[17:07:17] 价格OCR识别: 免费服务 (置信度: 1.000)
[17:07:17] ✅ 价格OCR识别完成，共识别到 21 个文本
[17:07:17] ✅ 提取到通用价格: 69.9 (来源: 限量低价￥69.9￥100)
[17:07:17] ✅ 获取到颜色 藏青色拼接两件套 的价格: 69.9
[17:07:17] 🔍 开始第2次坐标校验OCR识别...
[17:07:17] 🔍 执行坐标校验OCR识别...
[17:07:17] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[17:07:17] ✅ OCR截图已保存: 商品图片\coordinate_verify.jpg
[17:07:17] 正在执行OCR识别...
[17:07:19] OCR原始结果类型: <class 'list'>
[17:07:19] OCR原始结果长度: 1
[17:07:19] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:07:19] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[17:07:19]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[17:07:19]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002B96976B1A0>
[17:07:19]   _rand_fn: <class 'NoneType'> - None
[17:07:19]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002B96C4286E0>
[17:07:19] 方式2成功：识别到 21 个文本
[17:07:19] 文本 0: '限量低价￥69.9￥100', 置信度: 0.9047
[17:07:19] OCR识别: 限量低价￥69.9￥100 (置信度: 0.9047)
[17:07:19] 文本 1: '1', 置信度: 0.9436
[17:07:19] OCR识别: 1 (置信度: 0.9436)
[17:07:19] 文本 2: '+', 置信度: 0.9959
[17:07:19] OCR识别: + (置信度: 0.9959)
[17:07:19] 文本 3: '同款热销即将恢复100元', 置信度: 0.9987
[17:07:19] OCR识别: 同款热销即将恢复100元 (置信度: 0.9987)
[17:07:19] 文本 4: '已选：藏青色拼接两件套140码 适合身高1米3…', 置信度: 0.9550
[17:07:19] OCR识别: 已选：藏青色拼接两件套140码 适合身高1米3… (置信度: 0.9550)
[17:07:19] 文本 5: '颜色分类', 置信度: 0.9998
[17:07:19] OCR识别: 颜色分类 (置信度: 0.9998)
[17:07:19] 文本 6: '参考分类', 置信度: 0.9999
[17:07:19] OCR识别: 参考分类 (置信度: 0.9999)
[17:07:19] 文本 7: '颜色分类', 置信度: 0.9999
[17:07:19] OCR识别: 颜色分类 (置信度: 0.9999)
[17:07:19] 文本 8: '快要抢光', 置信度: 0.9983
[17:07:19] OCR识别: 快要抢光 (置信度: 0.9983)
[17:07:19] 文本 9: '藏青色拼接两件', 置信度: 0.9981
[17:07:19] OCR识别: 藏青色拼接两件 (置信度: 0.9981)
[17:07:19] 文本 10: '套', 置信度: 0.9999
[17:07:19] OCR识别: 套 (置信度: 0.9999)
[17:07:19] 文本 11: '黑色印花T恤【单】', 置信度: 0.9921
[17:07:19] OCR识别: 黑色印花T恤【单】 (置信度: 0.9921)
[17:07:19] 文本 12: '参考分类', 置信度: 0.9999
[17:07:19] OCR识别: 参考分类 (置信度: 0.9999)
[17:07:19] 文本 13: '110码适合身高1米05左右', 置信度: 0.9990
[17:07:19] OCR识别: 110码适合身高1米05左右 (置信度: 0.9990)
[17:07:19] 文本 14: '120码适合身高1米15左右', 置信度: 0.9968
[17:07:19] OCR识别: 120码适合身高1米15左右 (置信度: 0.9968)
[17:07:19] 文本 15: '130码适合身高1米25左右', 置信度: 0.9993
[17:07:19] OCR识别: 130码适合身高1米25左右 (置信度: 0.9993)
[17:07:19] 文本 16: '140码适合身高1米35左右', 置信度: 0.9995
[17:07:19] OCR识别: 140码适合身高1米35左右 (置信度: 0.9995)
[17:07:19] 文本 17: '150码适合身高1米45左右', 置信度: 0.9972
[17:07:19] OCR识别: 150码适合身高1米45左右 (置信度: 0.9972)
[17:07:19] 文本 18: '160码适合身高1米5左右', 置信度: 0.9996
[17:07:19] OCR识别: 160码适合身高1米5左右 (置信度: 0.9996)
[17:07:19] 文本 19: '170码适合身高1米58左右', 置信度: 0.9996
[17:07:19] OCR识别: 170码适合身高1米58左右 (置信度: 0.9996)
[17:07:19] 文本 20: '免费服务', 置信度: 0.9999
[17:07:19] OCR识别: 免费服务 (置信度: 0.9999)
[17:07:19] ✅ OCR识别完成，共识别到 21 个文本
[17:07:19] 🧠 开始智能分析商品信息...
[17:07:20] 🔧 开始智能拼接被分割的文本...
[17:07:20] 🎯 找到上方紧贴文本:
[17:07:20]    上方文本: '颜色分类' Y范围: 105.0-134.0, X范围: 2.0-73.0
[17:07:20]    当前文本: '颜色分类' Y范围: 144.0-169.0, X起始: 2.0
[17:07:20]    垂直间距: 10.0px, X重叠: True
[17:07:20] ✅ 向上拼接: '颜色分类' + '颜色分类' = '颜色分类颜色分类'
[17:07:20] 🔗 完成向上拼接:
[17:07:20]    结果: '颜色分类颜色分类'
[17:07:20] 🎯 找到上方紧贴文本:
[17:07:20]    上方文本: '颜色分类' Y范围: 144.0-169.0, X范围: 2.0-74.0
[17:07:20]    当前文本: '快要抢光' Y范围: 172.0-195.0, X起始: 5.0
[17:07:20]    垂直间距: 3.0px, X重叠: True
[17:07:20] ✅ 向上拼接: '颜色分类' + '快要抢光' = '颜色分类快要抢光'
[17:07:20] 🔗 完成向上拼接:
[17:07:20]    结果: '颜色分类快要抢光'
[17:07:20] 🎯 找到上方紧贴文本:
[17:07:20]    上方文本: '藏青色拼接两件' Y范围: 302.0-320.0, X范围: 25.0-128.0
[17:07:20]    当前文本: '套' Y范围: 318.0-335.0, X起始: 62.0
[17:07:20]    垂直间距: 2.0px, X重叠: True
[17:07:20] ✅ 向上拼接: '藏青色拼接两件' + '套' = '藏青色拼接两件套'
[17:07:20] 🔗 完成向上拼接:
[17:07:20]    结果: '藏青色拼接两件套'
[17:07:20] 🎯 找到上方紧贴文本:
[17:07:20]    上方文本: '藏青色拼接两件' Y范围: 302.0-320.0, X范围: 25.0-128.0
[17:07:20]    当前文本: '黑色印花T恤【单】' Y范围: 308.0-330.0, X起始: 150.0
[17:07:20]    垂直间距: 12.0px, X重叠: True
[17:07:20] ✅ 向上拼接: '藏青色拼接两件' + '黑色印花T恤【单】' = '藏青色拼接两件黑色印花T恤【单】'
[17:07:20] 🔗 完成向上拼接:
[17:07:20]    结果: '藏青色拼接两件黑色印花T恤【单】'
[17:07:20] 📊 拼接结果: 原始21个文本 → 拼接后19个文本
[17:07:20] 📝 拼接后的文本列表:
[17:07:20]    1. '限量低价￥69.9￥100'
[17:07:20]    2. '1'
[17:07:20]    3. '+'
[17:07:20]    4. '同款热销即将恢复100元'
[17:07:20]    5. '已选：藏青色拼接两件套140码 适合身高1米3…'
[17:07:20]    6. '参考分类'
[17:07:20]    7. '颜色分类颜色分类'
[17:07:20]    8. '颜色分类快要抢光'
[17:07:20]    9. '藏青色拼接两件套'
[17:07:20]    10. '藏青色拼接两件黑色印花T恤【单】'
[17:07:20]    11. '参考分类'
[17:07:20]    12. '110码适合身高1米05左右'
[17:07:20]    13. '120码适合身高1米15左右'
[17:07:20]    14. '130码适合身高1米25左右'
[17:07:20]    15. '140码适合身高1米35左右'
[17:07:20]    16. '150码适合身高1米45左右'
[17:07:20]    17. '160码适合身高1米5左右'
[17:07:20]    18. '170码适合身高1米58左右'
[17:07:20]    19. '免费服务'
[17:07:20] 🎯 找到尺码区域开始位置: 第6行 '参考分类'
[17:07:20] 📝 尺码区域文本无数字: '颜色分类颜色分类'
[17:07:20] 📝 尺码区域文本无数字: '颜色分类快要抢光'
[17:07:20] 📝 尺码区域文本无数字: '藏青色拼接两件套'
[17:07:20] 📝 尺码区域文本无数字: '藏青色拼接两件黑色印花T恤【单】'
[17:07:20] 📝 尺码区域文本无数字: '参考分类'
[17:07:20] ✅ 提取尺码: 110 (来源: '110码适合身高1米05左右')
[17:07:20] 🔍 处理bbox: [11, 384, 201, 402] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (106, 393)
[17:07:20] 📍 记录尺码坐标: 110 -> (106, 393)
[17:07:20] ✅ 提取尺码: 120 (来源: '120码适合身高1米15左右')
[17:07:20] 🔍 处理bbox: [9, 424, 202, 446] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (105, 435)
[17:07:20] 📍 记录尺码坐标: 120 -> (105, 435)
[17:07:20] ✅ 提取尺码: 130 (来源: '130码适合身高1米25左右')
[17:07:20] 🔍 处理bbox: [10, 466, 203, 488] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (106, 477)
[17:07:20] 📍 记录尺码坐标: 130 -> (106, 477)
[17:07:20] ✅ 提取尺码: 140 (来源: '140码适合身高1米35左右')
[17:07:20] 🔍 处理bbox: [10, 507, 204, 529] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (107, 518)
[17:07:20] 📍 记录尺码坐标: 140 -> (107, 518)
[17:07:20] ✅ 提取尺码: 150 (来源: '150码适合身高1米45左右')
[17:07:20] 🔍 处理bbox: [11, 551, 202, 569] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (106, 560)
[17:07:20] 📍 记录尺码坐标: 150 -> (106, 560)
[17:07:20] ✅ 提取尺码: 160 (来源: '160码适合身高1米5左右')
[17:07:20] 🔍 处理bbox: [10, 591, 195, 612] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (102, 601)
[17:07:20] 📍 记录尺码坐标: 160 -> (102, 601)
[17:07:20] ✅ 提取尺码: 170 (来源: '170码适合身高1米58左右')
[17:07:20] 🔍 处理bbox: [212, 588, 410, 616] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (311, 602)
[17:07:20] 📍 记录尺码坐标: 170 -> (311, 602)
[17:07:20] 🛑 遇到停止关键词，结束尺码提取: '免费服务'
[17:07:20] 📊 尺码提取结果: 数字=[110, 120, 130, 140, 150, 160, 170], 范围=110-170, 原始文本数量=7
[17:07:20] 尺码信息: {'optimized_range': '110-170', 'original_texts': ['110码适合身高1米05左右', '120码适合身高1米15左右', '130码适合身高1米25左右', '140码适合身高1米35左右', '150码适合身高1米45左右', '160码适合身高1米5左右', '170码适合身高1米58左右'], 'size_numbers': [110, 120, 130, 140, 150, 160, 170]}
[17:07:20] 📍 已记录尺码坐标: {110: (106, 393), 120: (105, 435), 130: (106, 477), 140: (107, 518), 150: (106, 560), 160: (102, 601), 170: (311, 602)}
[17:07:20] 🔍 检测到重复'颜色分类'文本(2次)，启用新格式过滤模式
[17:07:20] 🔍 发现颜色分类嵌入格式: '颜色分类颜色分类' → 提取: '颜色分类'
[17:07:20] 🎯 发现嵌入颜色分类: '颜色分类' (来源: '颜色分类颜色分类')
[17:07:20] 🔍 记录颜色分类位置: 第6行 '颜色分类颜色分类'
[17:07:20] 🔍 发现颜色分类嵌入格式: '颜色分类快要抢光' → 提取: '快要抢光'
[17:07:20] ⚠️ 嵌入颜色清理后为空: '快要抢光' (来源: '颜色分类快要抢光')
[17:07:20] 🔍 记录颜色分类位置: 第7行 '颜色分类快要抢光'
[17:07:20] 🎯 选择最后一个颜色分类作为开始位置: 第7行
[17:07:20] 🎯 找到颜色分类结束位置: 第10行 '参考分类'
[17:07:20] 🔍 开始提取颜色分类: 从第8行到第9行
[17:07:20] ✅ 保留有效文本: '藏青色拼接两件套'
[17:07:20] ✅ 保留有效文本: '藏青色拼接两件黑色印花T恤【单】'
[17:07:20] ✅ 添加嵌入颜色分类: '颜色分类'
[17:07:20] 🔍 新格式过滤完成: 从颜色分类区间提取到 3 个有效文本
[17:07:20]    1. '藏青色拼接两件套'
[17:07:20]    2. '藏青色拼接两件黑色印花T恤【单】'
[17:07:20]    3. '颜色分类'
[17:07:20] 🔍 检查颜色文本: '藏青色拼接两件套' (长度: 8)
[17:07:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 25 ... 320]
[17:07:20] 🔍 价格检测结果: False
[17:07:20] 🔍 处理bbox: [25, 302, 128, 320] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (76, 311)
[17:07:20] 📍 记录颜色坐标: 藏青色拼接两件套 -> (76, 311)
[17:07:20] ✅ 提取到无价格颜色: 藏青色拼接两件套
[17:07:20] 🔍 检查颜色文本: '藏青色拼接两件黑色印花T恤【单】' (长度: 16)
[17:07:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 25 ... 320]
[17:07:20] 🔍 价格检测结果: False
[17:07:20] 🔍 处理bbox: [25, 302, 128, 320] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (76, 311)
[17:07:20] 📍 记录颜色坐标: 藏青色拼接两件黑色印花T恤【单】 -> (76, 311)
[17:07:20] ✅ 提取到无价格颜色: 藏青色拼接两件黑色印花T恤【单】
[17:07:20] 🔍 检查颜色文本: '颜色分类' (长度: 4)
[17:07:20] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  2 ... 134]
[17:07:20] 🔍 价格检测结果: False
[17:07:20] 🔍 处理bbox: [2, 105, 73, 134] (长度: 4)
[17:07:20] ✅ 计算坐标成功: (37, 119)
[17:07:20] 📍 记录颜色坐标: 颜色分类 -> (37, 119)
[17:07:20] ✅ 提取到无价格颜色: 颜色分类
[17:07:20] 🎨 颜色分类提取完成: 共提取到 3 个颜色
[17:07:20] 📍 坐标记录完成: 共记录 3 个坐标
[17:07:20] 提取到颜色分类: [{'pure_name': '藏青色拼接两件套', 'original_text': '藏青色拼接两件套', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 25, ..., 320], dtype=int16)}, {'pure_name': '藏青色拼接两件黑色印花T恤【单】', 'original_text': '藏青色拼接两件黑色印花T恤【单】', 'has_direct_price': False, 'direct_price': None, 'bbox': array([ 25, ..., 320], dtype=int16)}, {'pure_name': '颜色分类', 'original_text': '颜色分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  2, ..., 134], dtype=int16)}]
[17:07:20] 颜色分类数量: 3
[17:07:20] 📍 已记录颜色坐标: {'藏青色拼接两件套': (76, 311), '藏青色拼接两件黑色印花T恤【单】': (76, 311), '颜色分类': (37, 119)}
[17:07:20] 🎯 从页面提取价格信息
[17:07:20] ✅ 页面通用价格: 69.9 (来源: 限量低价￥69.9￥100)
[17:07:20] 🔧 不分配页面价格给颜色，使用交互式方案获取准确价格
[17:07:20] 🔍 商品类型分析:
[17:07:20]    颜色数量: 3
[17:07:20]    颜色直接带价格: False
[17:07:20]    页面有券前/券后价格: False
[17:07:20]    尺码带价格: False
[17:07:20] 📊 分析类型: type3_multiple_colors_no_prices
[17:07:20] 🔧 处理方案: interactive
[17:07:20] 📊 坐标对比结果（仅对比原有颜色）:
[17:07:20]    藏青色拼接两件套: (76, 311) → (76, 311) (差异: X±0, Y±0)
[17:07:20]    ✅ 藏青色拼接两件套 坐标无显著变化
[17:07:20]    藏青色拼接两件黑色印花T恤【单】: (76, 311) → (76, 311) (差异: X±0, Y±0)
[17:07:20]    ✅ 藏青色拼接两件黑色印花T恤【单】 坐标无显著变化
[17:07:20]    颜色分类: (38, 119) → (37, 119) (差异: X±1, Y±0)
[17:07:20]    ✅ 颜色分类 坐标无显著变化
[17:07:20] ✅ 所有颜色坐标无显著变化，继续使用原始坐标
[17:07:20] ✅ 坐标无变化，继续使用原始坐标
[17:07:20] 🎨 处理颜色 2/3: 藏青色拼接两件黑色印花T恤【单】
[17:07:20] 📍 使用记录的坐标: 藏青色拼接两件黑色印花T恤【单】
[17:07:20]    相对坐标: (76, 311)
[17:07:20]    绝对坐标: (346, 541)
[17:07:20] 🎯 移动到颜色 藏青色拼接两件黑色印花T恤【单】 坐标: (346, 541)
[17:07:21] 🎯 点击颜色 藏青色拼接两件黑色印花T恤【单】
[17:07:23] 📸 截取颜色 藏青色拼接两件黑色印花T恤【单】 更新后的页面...
[17:07:23] ✅ 价格OCR截图已保存: 商品图片\1-2.jpg
[17:07:25] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:07:25] 字典格式价格OCR识别到 21 个文本
[17:07:25] 价格OCR识别: ¥28 - 69.9 (置信度: 0.851)
[17:07:25] 价格OCR识别: 1 (置信度: 0.998)
[17:07:25] 价格OCR识别: + (置信度: 0.995)
[17:07:25] 价格OCR识别: 同款热销 (置信度: 0.999)
[17:07:25] 价格OCR识别: 请选择：颜色分类 (置信度: 0.976)
[17:07:25] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:25] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:25] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:25] 价格OCR识别: 快要抢光 (置信度: 0.998)
[17:07:25] 价格OCR识别: 藏青色拼接两件 (置信度: 0.998)
[17:07:25] 价格OCR识别: 套 (置信度: 0.998)
[17:07:25] 价格OCR识别: 黑色印花T恤【单】 (置信度: 0.990)
[17:07:25] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:25] 价格OCR识别: 110码适合身高1米05左右 (置信度: 0.999)
[17:07:25] 价格OCR识别: 120码适合身高1米15左右 (置信度: 0.997)
[17:07:25] 价格OCR识别: 130码适合身高1米25左右 (置信度: 0.999)
[17:07:25] 价格OCR识别: 140码适合身高1米35左右 (置信度: 1.000)
[17:07:25] 价格OCR识别: 150码适合身高1米45左右 (置信度: 0.999)
[17:07:25] 价格OCR识别: 160码适合身高1米5左右 (置信度: 1.000)
[17:07:25] 价格OCR识别: 170码适合身高1米58左右 (置信度: 1.000)
[17:07:25] 价格OCR识别: 免费服务 (置信度: 1.000)
[17:07:25] ✅ 价格OCR识别完成，共识别到 21 个文本
[17:07:25] ⚠️ 未能从OCR结果中提取颜色 藏青色拼接两件黑色印花T恤【单】 的价格
[17:07:25] 🎨 处理颜色 3/3: 颜色分类
[17:07:25] 📍 使用记录的坐标: 颜色分类
[17:07:25]    相对坐标: (38, 119)
[17:07:25]    绝对坐标: (308, 349)
[17:07:25] 🎯 移动到颜色 颜色分类 坐标: (308, 349)
[17:07:26] 🎯 点击颜色 颜色分类
[17:07:28] 📸 截取颜色 颜色分类 更新后的页面...
[17:07:28] ✅ 价格OCR截图已保存: 商品图片\1-3.jpg
[17:07:30] 价格OCR结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:07:30] 字典格式价格OCR识别到 21 个文本
[17:07:30] 价格OCR识别: ¥28 - 69.9 (置信度: 0.856)
[17:07:30] 价格OCR识别: 1 (置信度: 0.998)
[17:07:30] 价格OCR识别: + (置信度: 0.995)
[17:07:30] 价格OCR识别: 同款热销 (置信度: 0.999)
[17:07:30] 价格OCR识别: 请选择：颜色分类 (置信度: 0.977)
[17:07:30] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:30] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:30] 价格OCR识别: 颜色分类 (置信度: 1.000)
[17:07:30] 价格OCR识别: 快要抢光 (置信度: 0.998)
[17:07:30] 价格OCR识别: 藏青色拼接两件 (置信度: 0.999)
[17:07:30] 价格OCR识别: 套 (置信度: 0.998)
[17:07:30] 价格OCR识别: 黑色印花T恤【单】 (置信度: 0.990)
[17:07:30] 价格OCR识别: 参考分类 (置信度: 1.000)
[17:07:30] 价格OCR识别: 110码适合身高1米05左右 (置信度: 0.999)
[17:07:30] 价格OCR识别: 120码适合身高1米15左右 (置信度: 0.999)
[17:07:30] 价格OCR识别: 130码适合身高1米25左右 (置信度: 0.999)
[17:07:30] 价格OCR识别: 140码适合身高1米35左右 (置信度: 1.000)
[17:07:30] 价格OCR识别: 150码适合身高1米45左右 (置信度: 0.999)
[17:07:30] 价格OCR识别: 160码适合身高1米5左右 (置信度: 1.000)
[17:07:30] 价格OCR识别: 170码适合身高1米58左右 (置信度: 1.000)
[17:07:30] 价格OCR识别: 免费服务 (置信度: 1.000)
[17:07:30] ✅ 价格OCR识别完成，共识别到 21 个文本
[17:07:30] ⚠️ 未能从OCR结果中提取颜色 颜色分类 的价格
[17:07:30] 💾 保存分析结果到Excel...
[17:07:30] 找到目标商品链接在第 2 行
[17:07:30] ✅ 保存有价格颜色: 藏青色拼接两件套 -> 69.9
[17:07:30] ✅ 保存有价格颜色: 藏青色拼接两件黑色印花T恤【单】 -> 获取失败
[17:07:30] ✅ 保存有价格颜色: 颜色分类 -> 获取失败
[17:07:30] 找到下一个商品链接在第 13 行
[17:07:30] 清空第 3 行到第 12 行中的 0 行有内容数据，保留空行
[17:07:30] ✅ 分析结果已保存到商品1下方: 商品图片\商品SKU信息.xlsx
[17:07:30]    插入了 5 行新数据
[17:07:30] 🎉 交互式价格获取完成
[17:07:30] ==================================================
[17:07:30] ✅ 智能OCR分析完成
[17:07:30] ==================================================
[17:07:30] ✅ 详情页图片捕获完成
[17:07:30] ✅ OCR分析和Excel保存已在详情页处理中完成
[17:07:30] ✅ 商品 1 处理完成
[17:07:30] 🔄 准备处理下一个商品...
[17:07:30] 🔄 开始返回搜索页面...
[17:07:30] 移动到位置(470,590)...
[17:07:31] 点击鼠标右键...
[17:07:32] 再次点击鼠标右键...
[17:07:33] ✅ 返回搜索页面操作完成
[17:07:33] 
==================================================
[17:07:33] 📍 开始处理商品 7 (2/2)
[17:07:33] 商品链接: https://mobile.yangkeduo.com/goods2.html?ps=J0eM5mnoXU
[17:07:33] ==================================================
[17:07:33] 步骤1: 检测页面状态...
[17:07:33] 🔍 检测页面是否存在SOUSUO2.png...
[17:07:33] 找到图片 SOUSUO2.png，位置: (641, 82)
[17:07:33] ✅ 页面状态正常，SOUSUO2.png存在
[17:07:33] 步骤2: 设置剪贴板内容...
[17:07:33] 已复制商品链接到剪贴板: https://mobile.yangkeduo.com/goods2.html?ps=J0eM5mnoXU
[17:07:34] ✅ 剪贴板内容验证成功
[17:07:34] 步骤3: 点击搜索框并输入链接...
[17:07:34] 移动到位置(480,96)并点击...
[17:07:34] 按下Ctrl+A全选...
[17:07:34] 执行AutoHotkey脚本: ctrl_a.ahk
[17:07:35] ✅ AutoHotkey脚本执行成功: ctrl_a
[17:07:36] 粘贴商品链接...
[17:07:36] 使用外部脚本软件执行真正的Ctrl+V...
[17:07:36] Python的键盘模拟无法被escrcpy识别，尝试其他方案
[17:07:36] 方法1: 尝试AutoHotkey...
[17:07:36] 找到脚本文件: paste_v2.ahk
[17:07:36] 检查路径: AutoHotkey.exe
[17:07:36] 检查路径: C:\Program Files\AutoHotkey\AutoHotkey.exe
[17:07:36] 检查路径: C:\Program Files (x86)\AutoHotkey\AutoHotkey.exe
[17:07:36] 检查路径: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[17:07:36] 找到AutoHotkey: C:\Program Files\AutoHotkey\v2\AutoHotkey.exe
[17:07:36] 执行AutoHotkey脚本: paste_v2.ahk
[17:07:37] AutoHotkey返回码: 0
[17:07:37] ✅ AutoHotkey执行成功
[17:07:37] ✅ Ctrl+V操作执行成功
[17:07:40] 步骤4: 点击搜索按钮...
[17:07:40] 正在查找图片: SOUSUO2.png (路径: image\IQOO\SOUSUO2.png)
[17:07:41] 找到图片 SOUSUO2.png，位置: (641, 82)
[17:07:41] 点击位置: (664, 97)
[17:07:41] 已点击图片: SOUSUO2.png
[17:07:41] 步骤4.5: 检测是否出现验证码...
[17:07:41] 等待2秒后检测验证码...
[17:07:43] 查找图片 YANZHENGMA.png 时出错: 
[17:07:43] ✅ 未检测到验证码，继续正常流程
[17:07:43] 步骤5: 等待3秒进入商品页面...
[17:07:46] 步骤6: 执行从右往左的滑动操作...
[17:07:46] 开始执行从右往左的滑动操作...
[17:07:46] 📍 起始位置: (630, 185)
[17:07:46] 📍 结束位置: (284, 185)
[17:07:46] 📏 滑动距离: 346 像素
[17:07:46] ⏱️ 滑动时长: 0.3 秒
[17:07:46] 移动到起始位置(630, 185)
[17:07:46] 按住左键从(630, 185)拖拽到(284, 185)
[17:07:47] 开始滑动，时长0.3秒...
[17:07:48] 释放左键，滑动操作完成
[17:07:48] ✅ 从右往左的滑动操作完成
[17:07:48] 步骤7: 移动到位置(470,185)...
[17:07:48] 已移动到位置(470,185)
[17:07:48] 步骤8: 执行鼠标点击和长按操作...
[17:07:48] 点击鼠标左键一次
[17:07:49] 在位置(475, 323)长按鼠标左键0.5秒
[17:07:50] 鼠标操作完成
[17:07:51] 步骤9: 查找并点击保存按钮...
[17:07:51] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[17:07:51] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:07:51] 找到图片 BAOCUN1.png，位置: (451, 808)
[17:07:51] 移动到图片上，等待0.5秒...
[17:07:52] 已点击图片: BAOCUN1.png
[17:07:52] 步骤10: 检测保存状态...
[17:07:52] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[17:07:52] 最多检测 80 次，每隔 0.2 秒检测一次
[17:07:52] 第 1/80 次检测...
[17:07:52] 第 2/80 次检测...
[17:07:53] 第 3/80 次检测...
[17:07:53] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 508)
[17:07:53] ✅ 检测到保存中状态，等待保存完成...
[17:07:53] 等待5秒让图片完全保存到手机...
[17:07:58] 步骤11: 复制手机图片到商品 7 文件夹...
[17:07:58] 第 1/3 次尝试复制图片...
[17:07:58] 开始从MTP设备复制图片到商品 7 文件夹...
[17:07:58] 正在使用Windows Shell API查找MTP设备...
[17:07:58] 找到设备: iQOO Z1
[17:07:58] 进入文件夹: 内部存储设备
[17:07:58] 进入文件夹: DCIM
[17:07:58] 进入文件夹: Pindd
[17:07:58] 进入文件夹: goods
[17:07:58] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[17:07:58] 目标路径: F:\自动捕获数据\商品图片\7
[17:07:58] 复制文件: 1755594472091--1237911638.jpg
[17:07:58] ✅ 成功复制: 1755594472091--1237911638.jpg
[17:07:58] 复制文件: 1755594472158-2084508500.jpg
[17:07:58] ✅ 成功复制: 1755594472158-2084508500.jpg
[17:07:58] 复制文件: 1755594472205-1672498435.jpg
[17:07:58] ✅ 成功复制: 1755594472205-1672498435.jpg
[17:07:58] 复制文件: 1755594472249--1741300258.jpg
[17:07:58] ✅ 成功复制: 1755594472249--1741300258.jpg
[17:07:58] 复制文件: 1755594472291-165444084.jpg
[17:07:58] ✅ 成功复制: 1755594472291-165444084.jpg
[17:07:58] 复制文件: 1755594472339-133002432.jpg
[17:07:58] ✅ 成功复制: 1755594472339-133002432.jpg
[17:07:58] 复制文件: 1755594472389-1165228246.jpg
[17:07:58] ✅ 成功复制: 1755594472389-1165228246.jpg
[17:07:58] 复制文件: 1755594472453-418658195.jpg
[17:07:58] ✅ 成功复制: 1755594472453-418658195.jpg
[17:07:58] 复制文件: 1755594472503--1643819477.jpg
[17:07:58] ✅ 成功复制: 1755594472503--1643819477.jpg
[17:07:58] 复制文件: 1755594472567-1192357437.jpg
[17:07:58] ✅ 成功复制: 1755594472567-1192357437.jpg
[17:07:58] 复制文件: 1755594472618--472194337.jpg
[17:07:58] ✅ 成功复制: 1755594472618--472194337.jpg
[17:07:58] 成功复制 11 个文件
[17:07:58] ✅ 成功复制 11 个图片到商品 7 文件夹
[17:07:58] 复制的文件:
[17:07:58]   - 1755594472091--1237911638.jpg
[17:07:58]   - 1755594472158-2084508500.jpg
[17:07:58]   - 1755594472205-1672498435.jpg
[17:07:58]   - 1755594472249--1741300258.jpg
[17:07:58]   - 1755594472291-165444084.jpg
[17:07:58]   - 1755594472339-133002432.jpg
[17:07:58]   - 1755594472389-1165228246.jpg
[17:07:58]   - 1755594472453-418658195.jpg
[17:07:58]   - 1755594472503--1643819477.jpg
[17:07:58]   - 1755594472567-1192357437.jpg
[17:07:58]   - 1755594472618--472194337.jpg
[17:07:58] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[17:07:58] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[17:07:58] ✅ 图片复制完成
[17:07:58] 步骤12: 删除手机上的原文件...
[17:07:58] 开始删除手机文件操作...
[17:07:58] 查找并点击GOODS.png...
[17:07:58] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[17:07:59] 找到图片 GOODS.png，位置: (790, 997)
[17:07:59] 点击位置: (815, 1006)
[17:07:59] 已点击图片: GOODS.png
[17:08:00] 使用AutoHotkey执行删除操作...
[17:08:00] 执行AutoHotkey脚本: delete_files.ahk
[17:08:03] ✅ AutoHotkey脚本执行成功: delete_files
[17:08:03] ✅ 删除操作执行成功
[17:08:03] ✅ 手机文件删除完成
[17:08:03] 步骤13: 开始处理主图...
[17:08:03] 步骤14: 开始第二轮操作（详情页）...
[17:08:03] 处理主图文件夹: 商品图片\7
[17:08:03] === 开始详情页图片捕获流程 ===
[17:08:03] 步骤13.1: 移动到(475,230)并点击右键...
[17:08:03] 找到 11 张图片
[17:08:03] 删除多余图片: 1755594472291-165444084.jpg
[17:08:03] 删除多余图片: 1755594472339-133002432.jpg
[17:08:03] 删除多余图片: 1755594472389-1165228246.jpg
[17:08:03] 删除多余图片: 1755594472453-418658195.jpg
[17:08:03] 删除多余图片: 1755594472503--1643819477.jpg
[17:08:03] 删除多余图片: 1755594472567-1192357437.jpg
[17:08:03] 删除多余图片: 1755594472618--472194337.jpg
[17:08:03] 重命名: 1755594472091--1237911638.jpg → 主图1.jpg
[17:08:03] 重命名: 1755594472158-2084508500.jpg → 主图2.jpg
[17:08:03] 重命名: 1755594472205-1672498435.jpg → 主图3.jpg
[17:08:03] 重命名: 1755594472249--1741300258.jpg → 主图4.jpg
[17:08:03] ✅ 主图处理完成
[17:08:05] 步骤13.2: 使用AutoHotkey向下滚动40次...
[17:08:05] 执行AutoHotkey脚本: scroll_down.ahk
[17:08:10] ✅ AutoHotkey脚本执行成功: scroll_down
[17:08:10] ✅ AutoHotkey滚动执行成功
[17:08:10] 步骤13.3: 移动到(477,300)并点击左键...
[17:08:10] ✅ 点击操作完成
[17:08:10] 步骤13.4: 移动到(480,495)并长按0.5秒...
[17:08:12] ✅ 长按操作完成
[17:08:13] 步骤13.5: 查找并点击保存按钮...
[17:08:13] 尝试查找图片 BAOCUN1.png (第1次/共3次)
[17:08:13] 正在查找图片: BAOCUN1.png (路径: image\IQOO\BAOCUN1.png)
[17:08:13] 找到图片 BAOCUN1.png，位置: (450, 808)
[17:08:13] 移动到图片上，等待0.5秒...
[17:08:14] 已点击图片: BAOCUN1.png
[17:08:14] 步骤13.6: 检测详情页保存状态...
[17:08:14] 开始检测图片: BAOCUNZHONG.png (路径: image\IQOO\BAOCUNZHONG.png)
[17:08:14] 最多检测 80 次，每隔 0.2 秒检测一次
[17:08:14] 第 1/80 次检测...
[17:08:14] 第 2/80 次检测...
[17:08:14] 第 3/80 次检测...
[17:08:14] 第 4/80 次检测...
[17:08:15] 第 5/80 次检测...
[17:08:15] 第 6/80 次检测...
[17:08:15] 第 7/80 次检测...
[17:08:16] 第 8/80 次检测...
[17:08:16] ✅ 找到图片 BAOCUNZHONG.png！位置: (443, 511)
[17:08:16] ✅ 检测到详情页保存中状态，等待保存完成...
[17:08:21] 步骤13.7: 复制详情页图片...
[17:08:21] 第 1/3 次尝试复制详情页图片...
[17:08:21] 开始从MTP设备复制图片到商品 7 文件夹...
[17:08:21] 正在使用Windows Shell API查找MTP设备...
[17:08:21] 找到设备: iQOO Z1
[17:08:21] 进入文件夹: 内部存储设备
[17:08:21] 进入文件夹: DCIM
[17:08:21] 进入文件夹: Pindd
[17:08:21] 进入文件夹: goods
[17:08:21] 成功找到目标文件夹: 内部存储设备\DCIM\Pindd\goods
[17:08:21] 目标路径: F:\自动捕获数据\商品图片\7
[17:08:21] 复制文件: 1755594493572--2131082317.jpg
[17:08:21] ✅ 成功复制: 1755594493572--2131082317.jpg
[17:08:21] 复制文件: 1755594493655--862326241.jpg
[17:08:21] ✅ 成功复制: 1755594493655--862326241.jpg
[17:08:21] 复制文件: 1755594493702--1274238900.jpg
[17:08:21] ✅ 成功复制: 1755594493702--1274238900.jpg
[17:08:21] 复制文件: 1755594493766-1220028732.jpg
[17:08:21] ✅ 成功复制: 1755594493766-1220028732.jpg
[17:08:21] 复制文件: 1755594493831-687015643.jpg
[17:08:21] ✅ 成功复制: 1755594493831-687015643.jpg
[17:08:21] 复制文件: 1755594493879-953526684.jpg
[17:08:21] ✅ 成功复制: 1755594493879-953526684.jpg
[17:08:21] 复制文件: 1755594493928--1499846839.jpg
[17:08:21] ✅ 成功复制: 1755594493928--1499846839.jpg
[17:08:21] 复制文件: 1755594493977--1612608463.jpg
[17:08:21] ✅ 成功复制: 1755594493977--1612608463.jpg
[17:08:21] 复制文件: 1755594494025--570499179.jpg
[17:08:21] ✅ 成功复制: 1755594494025--570499179.jpg
[17:08:21] 复制文件: 1755594494075--1373126789.jpg
[17:08:21] ✅ 成功复制: 1755594494075--1373126789.jpg
[17:08:21] 复制文件: 1755594494140-164084871.jpg
[17:08:21] ✅ 成功复制: 1755594494140-164084871.jpg
[17:08:21] 复制文件: 1755594494222--1141392258.jpg
[17:08:21] ✅ 成功复制: 1755594494222--1141392258.jpg
[17:08:21] 复制文件: 1755594494287--1603789870.jpg
[17:08:22] ✅ 成功复制: 1755594494287--1603789870.jpg
[17:08:22] 复制文件: 1755594494335--507125658.jpg
[17:08:22] ✅ 成功复制: 1755594494335--507125658.jpg
[17:08:22] 复制文件: 1755594494385--850550186.jpg
[17:08:22] ✅ 成功复制: 1755594494385--850550186.jpg
[17:08:22] 复制文件: 1755594494432--731661759.jpg
[17:08:22] ✅ 成功复制: 1755594494432--731661759.jpg
[17:08:22] 复制文件: 1755594494482--1561748410.jpg
[17:08:22] ✅ 成功复制: 1755594494482--1561748410.jpg
[17:08:22] 复制文件: 1755594494547-228221132.jpg
[17:08:22] ✅ 成功复制: 1755594494547-228221132.jpg
[17:08:22] 复制文件: 1755594494595--305811226.jpg
[17:08:22] ✅ 成功复制: 1755594494595--305811226.jpg
[17:08:22] 复制文件: 1755594494645-2077187725.jpg
[17:08:22] ✅ 成功复制: 1755594494645-2077187725.jpg
[17:08:22] 复制文件: 1755594494705--415663439.jpg
[17:08:22] ✅ 成功复制: 1755594494705--415663439.jpg
[17:08:22] 复制文件: 1755594494770-281993114.jpg
[17:08:22] ✅ 成功复制: 1755594494770-281993114.jpg
[17:08:22] 复制文件: 1755594494819--1806634095.jpg
[17:08:22] ✅ 成功复制: 1755594494819--1806634095.jpg
[17:08:22] 复制文件: 1755594494868--1639575155.jpg
[17:08:22] ✅ 成功复制: 1755594494868--1639575155.jpg
[17:08:22] 复制文件: 1755594494933-1352489201.jpg
[17:08:22] ✅ 成功复制: 1755594494933-1352489201.jpg
[17:08:22] 复制文件: 1755594494981-591155817.jpg
[17:08:22] ✅ 成功复制: 1755594494981-591155817.jpg
[17:08:22] 复制文件: 1755594495063-788168569.jpg
[17:08:22] ✅ 成功复制: 1755594495063-788168569.jpg
[17:08:22] 复制文件: 1755594495142-1189695085.jpg
[17:08:22] ✅ 成功复制: 1755594495142-1189695085.jpg
[17:08:22] 复制文件: 1755594495206--2049666727.jpg
[17:08:22] ✅ 成功复制: 1755594495206--2049666727.jpg
[17:08:22] 复制文件: 1755594495271--1867723096.jpg
[17:08:22] ✅ 成功复制: 1755594495271--1867723096.jpg
[17:08:22] 复制文件: 1755594495321-357029549.jpg
[17:08:22] ✅ 成功复制: 1755594495321-357029549.jpg
[17:08:22] 复制文件: 1755594495370--599379123.jpg
[17:08:22] ✅ 成功复制: 1755594495370--599379123.jpg
[17:08:22] 复制文件: 1755594495435--30287127.jpg
[17:08:23] ✅ 成功复制: 1755594495435--30287127.jpg
[17:08:23] 复制文件: 1755594495484-1229098288.jpg
[17:08:23] ✅ 成功复制: 1755594495484-1229098288.jpg
[17:08:23] 复制文件: 1755594495548-1540897692.jpg
[17:08:23] ✅ 成功复制: 1755594495548-1540897692.jpg
[17:08:23] 复制文件: 1755594495613-762033436.jpg
[17:08:23] ✅ 成功复制: 1755594495613-762033436.jpg
[17:08:23] 复制文件: 1755594495663--806930435.jpg
[17:08:23] ✅ 成功复制: 1755594495663--806930435.jpg
[17:08:23] 成功复制 37 个文件
[17:08:23] ✅ 成功复制 37 个图片到商品 7 文件夹
[17:08:23] 复制的文件:
[17:08:23]   - 1755594493572--2131082317.jpg
[17:08:23]   - 1755594493655--862326241.jpg
[17:08:23]   - 1755594493702--1274238900.jpg
[17:08:23]   - 1755594493766-1220028732.jpg
[17:08:23]   - 1755594493831-687015643.jpg
[17:08:23]   - 1755594493879-953526684.jpg
[17:08:23]   - 1755594493928--1499846839.jpg
[17:08:23]   - 1755594493977--1612608463.jpg
[17:08:23]   - 1755594494025--570499179.jpg
[17:08:23]   - 1755594494075--1373126789.jpg
[17:08:23]   - 1755594494140-164084871.jpg
[17:08:23]   - 1755594494222--1141392258.jpg
[17:08:23]   - 1755594494287--1603789870.jpg
[17:08:23]   - 1755594494335--507125658.jpg
[17:08:23]   - 1755594494385--850550186.jpg
[17:08:23]   - 1755594494432--731661759.jpg
[17:08:23]   - 1755594494482--1561748410.jpg
[17:08:23]   - 1755594494547-228221132.jpg
[17:08:23]   - 1755594494595--305811226.jpg
[17:08:23]   - 1755594494645-2077187725.jpg
[17:08:23]   - 1755594494705--415663439.jpg
[17:08:23]   - 1755594494770-281993114.jpg
[17:08:23]   - 1755594494819--1806634095.jpg
[17:08:23]   - 1755594494868--1639575155.jpg
[17:08:23]   - 1755594494933-1352489201.jpg
[17:08:23]   - 1755594494981-591155817.jpg
[17:08:23]   - 1755594495063-788168569.jpg
[17:08:23]   - 1755594495142-1189695085.jpg
[17:08:23]   - 1755594495206--2049666727.jpg
[17:08:23]   - 1755594495271--1867723096.jpg
[17:08:23]   - 1755594495321-357029549.jpg
[17:08:23]   - 1755594495370--599379123.jpg
[17:08:23]   - 1755594495435--30287127.jpg
[17:08:23]   - 1755594495484-1229098288.jpg
[17:08:23]   - 1755594495548-1540897692.jpg
[17:08:23]   - 1755594495613-762033436.jpg
[17:08:23]   - 1755594495663--806930435.jpg
[17:08:23] ⚠️  注意：由于MTP协议限制，无法自动删除手机上的原文件
[17:08:23] 请手动删除手机 DCIM/Pindd/goods 文件夹中的图片以释放空间
[17:08:23] ✅ 详情页图片复制完成
[17:08:23] 步骤13.8: 删除手机上的详情页图片...
[17:08:23] 开始删除手机文件操作...
[17:08:23] 查找并点击GOODS.png...
[17:08:23] 正在查找图片: GOODS.png (路径: image\IQOO\GOODS.png)
[17:08:23] 找到图片 GOODS.png，位置: (790, 997)
[17:08:23] 点击位置: (815, 1006)
[17:08:23] 已点击图片: GOODS.png
[17:08:24] 使用AutoHotkey执行删除操作...
[17:08:24] 执行AutoHotkey脚本: delete_files.ahk
[17:08:28] ✅ AutoHotkey脚本执行成功: delete_files
[17:08:28] ✅ 删除操作执行成功
[17:08:28] ✅ 手机详情页文件删除完成
[17:08:28] 步骤13.8: 处理详情页图片...
[17:08:28] 处理详情页图片文件夹: 商品图片\7
[17:08:28] 跳过已处理的主图: 主图1.jpg
[17:08:28] 跳过已处理的主图: 主图2.jpg
[17:08:28] 跳过已处理的主图: 主图3.jpg
[17:08:28] 跳过已处理的主图: 主图4.jpg
[17:08:28] 找到 37 张详情页图片
[17:08:28] 删除多余详情页图片: 1755594495321-357029549.jpg
[17:08:28] 删除多余详情页图片: 1755594495370--599379123.jpg
[17:08:28] 删除多余详情页图片: 1755594495435--30287127.jpg
[17:08:28] 删除多余详情页图片: 1755594495484-1229098288.jpg
[17:08:28] 删除多余详情页图片: 1755594495548-1540897692.jpg
[17:08:28] 删除多余详情页图片: 1755594495613-762033436.jpg
[17:08:28] 删除多余详情页图片: 1755594495663--806930435.jpg
[17:08:28] 重命名详情页图片: 1755594493572--2131082317.jpg → 1.jpg
[17:08:28] 重命名详情页图片: 1755594493655--862326241.jpg → 2.jpg
[17:08:28] 重命名详情页图片: 1755594493702--1274238900.jpg → 3.jpg
[17:08:28] 重命名详情页图片: 1755594493766-1220028732.jpg → 4.jpg
[17:08:28] 重命名详情页图片: 1755594493831-687015643.jpg → 5.jpg
[17:08:28] 重命名详情页图片: 1755594493879-953526684.jpg → 6.jpg
[17:08:28] 重命名详情页图片: 1755594493928--1499846839.jpg → 7.jpg
[17:08:28] 重命名详情页图片: 1755594493977--1612608463.jpg → 8.jpg
[17:08:28] 重命名详情页图片: 1755594494025--570499179.jpg → 9.jpg
[17:08:28] 重命名详情页图片: 1755594494075--1373126789.jpg → 10.jpg
[17:08:28] 重命名详情页图片: 1755594494140-164084871.jpg → 11.jpg
[17:08:28] 重命名详情页图片: 1755594494222--1141392258.jpg → 12.jpg
[17:08:28] 重命名详情页图片: 1755594494287--1603789870.jpg → 13.jpg
[17:08:28] 重命名详情页图片: 1755594494335--507125658.jpg → 14.jpg
[17:08:28] 重命名详情页图片: 1755594494385--850550186.jpg → 15.jpg
[17:08:28] 重命名详情页图片: 1755594494432--731661759.jpg → 16.jpg
[17:08:28] 重命名详情页图片: 1755594494482--1561748410.jpg → 17.jpg
[17:08:28] 重命名详情页图片: 1755594494547-228221132.jpg → 18.jpg
[17:08:28] 重命名详情页图片: 1755594494595--305811226.jpg → 19.jpg
[17:08:28] 重命名详情页图片: 1755594494645-2077187725.jpg → 20.jpg
[17:08:28] 重命名详情页图片: 1755594494705--415663439.jpg → 21.jpg
[17:08:28] 重命名详情页图片: 1755594494770-281993114.jpg → 22.jpg
[17:08:28] 重命名详情页图片: 1755594494819--1806634095.jpg → 23.jpg
[17:08:28] 重命名详情页图片: 1755594494868--1639575155.jpg → 24.jpg
[17:08:28] 重命名详情页图片: 1755594494933-1352489201.jpg → 25.jpg
[17:08:28] 重命名详情页图片: 1755594494981-591155817.jpg → 26.jpg
[17:08:28] 重命名详情页图片: 1755594495063-788168569.jpg → 27.jpg
[17:08:28] 重命名详情页图片: 1755594495142-1189695085.jpg → 28.jpg
[17:08:28] 重命名详情页图片: 1755594495206--2049666727.jpg → 29.jpg
[17:08:28] 重命名详情页图片: 1755594495271--1867723096.jpg → 30.jpg
[17:08:28] ✅ 详情页图片处理完成
[17:08:28] ✅ 主图保持'主图X'格式，详情页图片重命名为1-30
[17:08:28] ✅ 详情页图片处理完成
[17:08:28] 步骤13.9: 执行最后的鼠标操作...
[17:08:28] 开始执行最后的鼠标操作序列...
[17:08:28] 移动到位置(475,125)...
[17:08:29] 点击右键...
[17:08:29] 等待1秒...
[17:08:30] 移动到位置(670,940)...
[17:08:31] 点击左键...
[17:08:31] 等待2.5秒让页面加载完成...
[17:08:33] ✅ 最后的鼠标操作序列完成
[17:08:33] 步骤13.10: 执行智能OCR分析...
[17:08:33] ==================================================
[17:08:33] 🧠 智能OCR分析开始
[17:08:33] ==================================================
[17:08:33] 🔍 开始智能OCR分析屏幕区域: (270, 230) 到 (691, 883)
[17:08:33] ✅ OCR截图已保存: 商品图片\7.jpg
[17:08:33] 正在执行OCR识别...
[17:08:35] OCR原始结果类型: <class 'list'>
[17:08:35] OCR原始结果长度: 1
[17:08:35] 第一页结果类型: <class 'paddlex.inference.pipelines.ocr.result.OCRResult'>
[17:08:35] OCRResult对象属性: ['_save_funcs', '_json_writer', '_rand_fn', '_img_writer']
[17:08:35]   _save_funcs: <class 'list'> - [<bound method JsonMixin.save_to_json of {'input_path': None, 'page_index': None, 'doc_preprocessor_...
[17:08:35]   _json_writer: <class 'paddlex.inference.utils.io.writers.JsonWriter'> - <paddlex.inference.utils.io.writers.JsonWriter object at 0x000002B96A2D30B0>
[17:08:35]   _rand_fn: <class 'NoneType'> - None
[17:08:35]   _img_writer: <class 'paddlex.inference.utils.io.writers.ImageWriter'> - <paddlex.inference.utils.io.writers.ImageWriter object at 0x000002B96AFBF8F0>
[17:08:35] 方式2成功：识别到 22 个文本
[17:08:35] 文本 0: '￥35.96- 69.23', 置信度: 0.8843
[17:08:35] OCR识别: ￥35.96- 69.23 (置信度: 0.8843)
[17:08:35] 文本 1: '+', 置信度: 0.9921
[17:08:35] OCR识别: + (置信度: 0.9921)
[17:08:35] 文本 2: '请选择：颜色分类参考分类', 置信度: 0.9965
[17:08:35] OCR识别: 请选择：颜色分类参考分类 (置信度: 0.9965)
[17:08:35] 文本 3: '颜色分类', 置信度: 0.9998
[17:08:35] OCR识别: 颜色分类 (置信度: 0.9998)
[17:08:35] 文本 4: '参考分类', 置信度: 0.9999
[17:08:35] OCR识别: 参考分类 (置信度: 0.9999)
[17:08:35] 文本 5: '颜色分类', 置信度: 0.9998
[17:08:35] OCR识别: 颜色分类 (置信度: 0.9998)
[17:08:35] 文本 6: '藏青色三件套', 置信度: 0.9989
[17:08:35] OCR识别: 藏青色三件套 (置信度: 0.9989)
[17:08:35] 文本 7: '焦糖色三件套', 置信度: 0.9990
[17:08:35] OCR识别: 焦糖色三件套 (置信度: 0.9990)
[17:08:35] 文本 8: '单件短裙黑色', 置信度: 0.9992
[17:08:35] OCR识别: 单件短裙黑色 (置信度: 0.9992)
[17:08:35] 文本 9: '￥69.23', 置信度: 0.9549
[17:08:35] OCR识别: ￥69.23 (置信度: 0.9549)
[17:08:35] 文本 10: '￥69.23', 置信度: 0.9428
[17:08:35] OCR识别: ￥69.23 (置信度: 0.9428)
[17:08:35] 文本 11: '￥35.96', 置信度: 0.9451
[17:08:35] OCR识别: ￥35.96 (置信度: 0.9451)
[17:08:35] 文本 12: '参考分类', 置信度: 0.9999
[17:08:35] OCR识别: 参考分类 (置信度: 0.9999)
[17:08:35] 文本 13: '90建议身高80-90', 置信度: 0.9990
[17:08:35] OCR识别: 90建议身高80-90 (置信度: 0.9990)
[17:08:35] 文本 14: '100建议身高90-100', 置信度: 0.9965
[17:08:35] OCR识别: 100建议身高90-100 (置信度: 0.9965)
[17:08:35] 文本 15: '110建议身高100-110', 置信度: 0.9972
[17:08:35] OCR识别: 110建议身高100-110 (置信度: 0.9972)
[17:08:35] 文本 16: '120建议身高110-120', 置信度: 0.9979
[17:08:35] OCR识别: 120建议身高110-120 (置信度: 0.9979)
[17:08:35] 文本 17: '130建议身高120-130', 置信度: 0.9979
[17:08:35] OCR识别: 130建议身高120-130 (置信度: 0.9979)
[17:08:35] 文本 18: '140建议身高130-140', 置信度: 0.9964
[17:08:35] OCR识别: 140建议身高130-140 (置信度: 0.9964)
[17:08:35] 文本 19: '免费服务', 置信度: 0.9998
[17:08:35] OCR识别: 免费服务 (置信度: 0.9998)
[17:08:35] 文本 20: '退货包运费(商家赠送）', 置信度: 0.9241
[17:08:35] OCR识别: 退货包运费(商家赠送） (置信度: 0.9241)
[17:08:35] 文本 21: '4一次选多款，不满意可退货包运费>', 置信度: 0.9623
[17:08:35] OCR识别: 4一次选多款，不满意可退货包运费> (置信度: 0.9623)
[17:08:35] ✅ OCR识别完成，共识别到 22 个文本
[17:08:35] 💾 保存OCR结果到文件...
[17:08:35] ✅ OCR结果已保存: OCR\商品7_OCR结果.txt
[17:08:35] 🧠 开始智能分析商品信息...
[17:08:35] 🔧 开始智能拼接被分割的文本...
[17:08:35] 🎯 找到上方紧贴文本:
[17:08:35]    上方文本: '颜色分类' Y范围: 84.0-106.0, X范围: 4.0-72.0
[17:08:35]    当前文本: '颜色分类' Y范围: 121.0-144.0, X起始: 4.0
[17:08:35]    垂直间距: 15.0px, X重叠: True
[17:08:35] ✅ 向上拼接: '颜色分类' + '颜色分类' = '颜色分类颜色分类'
[17:08:35] 🔗 完成向上拼接:
[17:08:35]    结果: '颜色分类颜色分类'
[17:08:35] 🎯 找到上方紧贴文本:
[17:08:35]    上方文本: '藏青色三件套' Y范围: 278.0-297.0, X范围: 37.0-119.0
[17:08:35]    当前文本: '￥69.23' Y范围: 293.0-312.0, X起始: 47.0
[17:08:36]    垂直间距: 4.0px, X重叠: True
[17:08:36] ✅ 向上拼接: '藏青色三件套' + '￥69.23' = '藏青色三件套￥69.23'
[17:08:36] 🔗 完成向上拼接:
[17:08:36]    结果: '藏青色三件套￥69.23'
[17:08:36] 🎯 找到上方紧贴文本:
[17:08:36]    上方文本: '焦糖色三件套' Y范围: 278.0-297.0, X范围: 168.0-249.0
[17:08:36]    当前文本: '￥69.23' Y范围: 294.0-310.0, X起始: 186.0
[17:08:36]    垂直间距: 3.0px, X重叠: True
[17:08:36] ✅ 向上拼接: '焦糖色三件套' + '￥69.23' = '焦糖色三件套￥69.23'
[17:08:36] 🔗 完成向上拼接:
[17:08:36]    结果: '焦糖色三件套￥69.23'
[17:08:36] 🎯 找到上方紧贴文本:
[17:08:36]    上方文本: '单件短裙黑色' Y范围: 278.0-297.0, X范围: 307.0-388.0
[17:08:36]    当前文本: '￥35.96' Y范围: 293.0-312.0, X起始: 323.0
[17:08:36]    垂直间距: 4.0px, X重叠: True
[17:08:36] ✅ 向上拼接: '单件短裙黑色' + '￥35.96' = '单件短裙黑色￥35.96'
[17:08:36] 🔗 完成向上拼接:
[17:08:36]    结果: '单件短裙黑色￥35.96'
[17:08:36] 🎯 找到上方紧贴文本:
[17:08:36]    上方文本: '参考分类' Y范围: 322.0-348.0, X范围: 2.0-74.0
[17:08:36]    当前文本: '90建议身高80-90' Y范围: 356.0-381.0, X起始: 9.0
[17:08:36]    垂直间距: 8.0px, X重叠: True
[17:08:36] ✅ 向上拼接: '参考分类' + '90建议身高80-90' = '参考分类90建议身高80-90'
[17:08:36] 🔗 完成向上拼接:
[17:08:36]    结果: '参考分类90建议身高80-90'
[17:08:36] 🎯 找到上方紧贴文本:
[17:08:36]    上方文本: '免费服务' Y范围: 481.0-506.0, X范围: 2.0-73.0
[17:08:36]    当前文本: '退货包运费(商家赠送）' Y范围: 516.0-538.0, X起始: 29.0
[17:08:36]    垂直间距: 10.0px, X重叠: True
[17:08:36] ✅ 向上拼接: '免费服务' + '退货包运费(商家赠送）' = '免费服务退货包运费(商家赠送）'
[17:08:36] 🔗 完成向上拼接:
[17:08:36]    结果: '免费服务退货包运费(商家赠送）'
[17:08:36] 📊 拼接结果: 原始22个文本 → 拼接后16个文本
[17:08:36] 📝 拼接后的文本列表:
[17:08:36]    1. '￥35.96- 69.23'
[17:08:36]    2. '+'
[17:08:36]    3. '请选择：颜色分类参考分类'
[17:08:36]    4. '参考分类'
[17:08:36]    5. '颜色分类颜色分类'
[17:08:36]    6. '藏青色三件套￥69.23'
[17:08:36]    7. '焦糖色三件套￥69.23'
[17:08:36]    8. '单件短裙黑色￥35.96'
[17:08:36]    9. '参考分类90建议身高80-90'
[17:08:36]    10. '100建议身高90-100'
[17:08:36]    11. '110建议身高100-110'
[17:08:36]    12. '120建议身高110-120'
[17:08:36]    13. '130建议身高120-130'
[17:08:36]    14. '140建议身高130-140'
[17:08:36]    15. '免费服务退货包运费(商家赠送）'
[17:08:36]    16. '4一次选多款，不满意可退货包运费>'
[17:08:36] 🎯 找到尺码区域开始位置: 第3行 '请选择：颜色分类参考分类'
[17:08:36] 📝 尺码区域文本无数字: '参考分类'
[17:08:36] 📝 尺码区域文本无数字: '颜色分类颜色分类'
[17:08:36] 🔧 过滤￥符号后的文本: '藏青色三件套'
[17:08:36] 📝 尺码区域文本无数字: '藏青色三件套'
[17:08:36] 🔧 过滤￥符号后的文本: '焦糖色三件套'
[17:08:36] 📝 尺码区域文本无数字: '焦糖色三件套'
[17:08:36] 🔧 过滤￥符号后的文本: '单件短裙黑色'
[17:08:36] 📝 尺码区域文本无数字: '单件短裙黑色'
[17:08:36] ✅ 提取尺码: 90 (来源: '参考分类90建议身高80-90')
[17:08:36] 🔍 处理bbox: [2, 322, 74, 348] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (38, 335)
[17:08:36] 📍 记录尺码坐标: 90 -> (38, 335)
[17:08:36] ✅ 提取尺码: 100 (来源: '100建议身高90-100')
[17:08:36] 🔍 处理bbox: [169, 359, 326, 378] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (247, 368)
[17:08:36] 📍 记录尺码坐标: 100 -> (247, 368)
[17:08:36] ✅ 提取尺码: 110 (来源: '110建议身高100-110')
[17:08:36] 🔍 处理bbox: [11, 402, 171, 420] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (91, 411)
[17:08:36] 📍 记录尺码坐标: 110 -> (91, 411)
[17:08:36] ✅ 提取尺码: 120 (来源: '120建议身高110-120')
[17:08:36] 🔍 处理bbox: [190, 400, 352, 421] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (271, 410)
[17:08:36] 📍 记录尺码坐标: 120 -> (271, 410)
[17:08:36] ✅ 提取尺码: 130 (来源: '130建议身高120-130')
[17:08:36] 🔍 处理bbox: [10, 442, 173, 463] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (91, 452)
[17:08:36] 📍 记录尺码坐标: 130 -> (91, 452)
[17:08:36] ✅ 提取尺码: 140 (来源: '140建议身高130-140')
[17:08:36] 🔍 处理bbox: [192, 442, 356, 463] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (274, 452)
[17:08:36] 📍 记录尺码坐标: 140 -> (274, 452)
[17:08:36] 🛑 遇到停止关键词，结束尺码提取: '免费服务退货包运费(商家赠送）'
[17:08:36] 📊 尺码提取结果: 数字=[90, 100, 110, 120, 130, 140], 范围=90-140, 原始文本数量=6
[17:08:36] 尺码信息: {'optimized_range': '90-140', 'original_texts': ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140'], 'size_numbers': [90, 100, 110, 120, 130, 140]}
[17:08:36] 📍 已记录尺码坐标: {110: (91, 411), 120: (271, 410), 130: (91, 452), 140: (274, 452), 150: (106, 560), 160: (102, 601), 170: (311, 602), 90: (38, 335), 100: (247, 368)}
[17:08:36] 🔍 检测到重复'颜色分类'文本(2次)，启用新格式过滤模式
[17:08:36] 🔍 发现颜色分类嵌入格式: '请选择：颜色分类参考分类' → 提取: '参考分类'
[17:08:36] 🔍 记录颜色分类位置: 第2行 '请选择：颜色分类参考分类'
[17:08:36] 🔍 发现颜色分类嵌入格式: '颜色分类颜色分类' → 提取: '颜色分类'
[17:08:36] 🎯 发现嵌入颜色分类: '颜色分类' (来源: '颜色分类颜色分类')
[17:08:36] 🔍 记录颜色分类位置: 第4行 '颜色分类颜色分类'
[17:08:36] 🎯 选择最后一个颜色分类作为开始位置: 第4行
[17:08:36] 🎯 找到颜色分类结束位置: 第8行 '参考分类90建议身高80-90'
[17:08:36] 🔍 开始提取颜色分类: 从第5行到第7行
[17:08:36] ✅ 保留有效文本: '藏青色三件套￥69.23'
[17:08:36] ✅ 保留有效文本: '焦糖色三件套￥69.23'
[17:08:36] ✅ 保留有效文本: '单件短裙黑色￥35.96'
[17:08:36] ✅ 添加嵌入颜色分类: '颜色分类'
[17:08:36] 🔍 新格式过滤完成: 从颜色分类区间提取到 4 个有效文本
[17:08:36]    1. '藏青色三件套￥69.23'
[17:08:36]    2. '焦糖色三件套￥69.23'
[17:08:36]    3. '单件短裙黑色￥35.96'
[17:08:36]    4. '颜色分类'
[17:08:36] 🔍 检查颜色文本: '藏青色三件套￥69.23' (长度: 12)
[17:08:36] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [ 37 ... 297]
[17:08:36] 🔍 价格检测结果: True
[17:08:36] 🔍 价格检测: 文本='藏青色三件套￥69.23', 检测到价格=True, 提取价格=69.23
[17:08:36] 🔍 处理bbox: [37, 278, 119, 297] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (78, 287)
[17:08:36] 📍 记录颜色坐标: 藏青色三件套 -> (78, 287)
[17:08:36] ✅ 提取到带价格颜色: 藏青色三件套 -> ¥69.23 (来源: 藏青色三件套￥69.23)
[17:08:36] 🔍 检查颜色文本: '焦糖色三件套￥69.23' (长度: 12)
[17:08:36] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [168 ... 297]
[17:08:36] 🔍 价格检测结果: True
[17:08:36] 🔍 价格检测: 文本='焦糖色三件套￥69.23', 检测到价格=True, 提取价格=69.23
[17:08:36] 🔍 处理bbox: [168, 278, 249, 297] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (208, 287)
[17:08:36] 📍 记录颜色坐标: 焦糖色三件套 -> (208, 287)
[17:08:36] ✅ 提取到带价格颜色: 焦糖色三件套 -> ¥69.23 (来源: 焦糖色三件套￥69.23)
[17:08:36] 🔍 检查颜色文本: '单件短裙黑色￥35.96' (长度: 12)
[17:08:36] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [307 ... 297]
[17:08:36] 🔍 价格检测结果: True
[17:08:36] 🔍 价格检测: 文本='单件短裙黑色￥35.96', 检测到价格=True, 提取价格=35.96
[17:08:36] 🔍 处理bbox: [307, 278, 388, 297] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (347, 287)
[17:08:36] 📍 记录颜色坐标: 单件短裙黑色 -> (347, 287)
[17:08:36] ✅ 提取到带价格颜色: 单件短裙黑色 -> ¥35.96 (来源: 单件短裙黑色￥35.96)
[17:08:36] 🔍 检查颜色文本: '颜色分类' (长度: 4)
[17:08:36] 🔍 bbox类型: <class 'numpy.ndarray'>, bbox内容: [  4 ... 106]
[17:08:36] 🔍 价格检测结果: False
[17:08:36] 🔍 处理bbox: [4, 84, 72, 106] (长度: 4)
[17:08:36] ✅ 计算坐标成功: (38, 95)
[17:08:36] 📍 记录颜色坐标: 颜色分类 -> (38, 95)
[17:08:36] ✅ 提取到无价格颜色: 颜色分类
[17:08:36] 🎨 颜色分类提取完成: 共提取到 4 个颜色
[17:08:36] 📍 坐标记录完成: 共记录 4 个坐标
[17:08:36] 提取到颜色分类: [{'pure_name': '藏青色三件套', 'original_text': '藏青色三件套￥69.23', 'has_direct_price': True, 'direct_price': '69.23', 'bbox': array([ 37, ..., 297], dtype=int16)}, {'pure_name': '焦糖色三件套', 'original_text': '焦糖色三件套￥69.23', 'has_direct_price': True, 'direct_price': '69.23', 'bbox': array([168, ..., 297], dtype=int16)}, {'pure_name': '单件短裙黑色', 'original_text': '单件短裙黑色￥35.96', 'has_direct_price': True, 'direct_price': '35.96', 'bbox': array([307, ..., 297], dtype=int16)}, {'pure_name': '颜色分类', 'original_text': '颜色分类', 'has_direct_price': False, 'direct_price': None, 'bbox': array([  4, ..., 106], dtype=int16)}]
[17:08:36] 颜色分类数量: 4
[17:08:36] 📍 已记录颜色坐标: {'藏青色三件套': (78, 287), '焦糖色三件套': (208, 287), '单件短裙黑色': (347, 287), '颜色分类': (38, 95)}
[17:08:36] 🎯 第一类商品：从颜色直接提取价格
[17:08:36] ✅ 直接价格: 藏青色三件套 -> 69.23
[17:08:36] ✅ 直接价格: 焦糖色三件套 -> 69.23
[17:08:36] ✅ 直接价格: 单件短裙黑色 -> 35.96
[17:08:36] 🔍 商品类型分析:
[17:08:36]    颜色数量: 4
[17:08:36]    颜色直接带价格: True
[17:08:36]    页面有券前/券后价格: False
[17:08:36]    尺码带价格: False
[17:08:36] 📊 分析类型: type1_multiple_colors_direct_prices
[17:08:36] 🔧 处理方案: basic
[17:08:36] 📊 智能分析结果:
[17:08:36]   优化尺码范围: 90-140
[17:08:36]   原始尺码文本: ['参考分类90建议身高80-90', '100建议身高90-100', '110建议身高100-110', '120建议身高110-120', '130建议身高120-130', '140建议身高130-140']
[17:08:36]   颜色分类: ['藏青色三件套', '焦糖色三件套', '单件短裙黑色', '颜色分类']
[17:08:36]   颜色价格: {'藏青色三件套': '69.23', '焦糖色三件套': '69.23', '单件短裙黑色': '35.96'}
[17:08:36]   分析类型: type1_multiple_colors_direct_prices
[17:08:36]   处理方案: basic
[17:08:36] 📝 使用基础处理方案，直接保存分析结果
[17:08:36] 💾 保存分析结果到Excel...
[17:08:36] 找到目标商品链接在第 73 行
[17:08:36] ✅ 保存有价格颜色: 藏青色三件套 -> 69.23
[17:08:36] ✅ 保存有价格颜色: 焦糖色三件套 -> 69.23
[17:08:36] ✅ 保存有价格颜色: 单件短裙黑色 -> 35.96
[17:08:36] ✅ 保存有价格颜色: 颜色分类 -> 未获取
[17:08:36] 找到下一个商品链接在第 84 行
[17:08:36] 清空第 74 行到第 83 行中的 0 行有内容数据，保留空行
[17:08:36] ✅ 分析结果已保存到商品7下方: 商品图片\商品SKU信息.xlsx
[17:08:36]    插入了 6 行新数据
[17:08:36] ==================================================
[17:08:36] ✅ 智能OCR分析完成
[17:08:36] ==================================================
[17:08:36] ✅ 详情页图片捕获完成
[17:08:36] ✅ OCR分析和Excel保存已在详情页处理中完成
[17:08:36] ✅ 商品 7 处理完成
[17:08:36] 
🎉 选中商品处理完成！成功: 2/2

